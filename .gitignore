# Dependencies
node_modules/
nest-app/node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
nest-app/package-lock.json
yarn.lock

# Environment variables
.env
.env.*
nest-app/.env
nest-app/.env.*
!.env.example
!nest-app/.env.example

# Compiled output
/dist
nest-app/dist
/build
nest-app/build

# Logs
logs/
*.log

# OS
.DS_Store

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Ansible
bbb-ansible/inventory/hosts.yml
bbb-ansible/**/*.retry

# Temporary files
*.tmp
*.temp
.tmp/
/out

# IDE and editors
/.idea
/.vscode
*.sublime-project
*.sublime-workspace
.project
.classpath
.c9/
*.launch
.settings/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
.directory

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Testing
/coverage
/.nyc_output
/test-results

# Temporary files
/tmp
/temp
*.tmp
*.temp

# Generated files
*.js.map
*.d.ts
tsconfig.tsbuildinfo

# Keep custom scripts in version control
# No scripts ignored
