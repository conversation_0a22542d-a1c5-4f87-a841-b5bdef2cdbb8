# BBB Manager

<img src="https://upload.wikimedia.org/wikipedia/commons/thumb/9/94/BigBlueButton_icon.svg/1024px-BigBlueButton_icon.svg.png" alt="BigBlueButton Logo" width="100" height="100">

## Overview

BBB Manager is an intelligent infrastructure management system that automatically provisions and decommissions BigBlueButton servers based on lecture schedules. It optimizes costs by using a bin-packing algorithm to minimize server count while ensuring optimal resource allocation.

## 🎯 Key Features

- **🕒 Smart Scheduling**: Analyzes lecture schedules and provisions servers 15 minutes before needed
- **📊 Resource Optimization**: Uses bin-packing algorithm to minimize infrastructure costs
- **🔄 Automatic Lifecycle**: Creates, configures, and destroys servers automatically
- **⚖️ Load Balancing**: Integrates with Scalelite for meeting distribution
- **📹 Recording Management**: Automated video recording with cloud storage
- **🌐 DNS Management**: Dynamic domain configuration for each server
- **📱 Real-time Monitoring**: Cache-based status tracking and health monitoring

## 🏗️ Architecture

```mermaid
graph TB
    subgraph "🎯 Control Center"
        API[🌐 NestJS API<br/>Port 3000<br/>Scheduler & Management]
        CACHE[💾 Redis Cache<br/>Lectures & Status]
        API <--> CACHE
    end

    subgraph "� Automation Engine"
        SCHED[📅 BBB Scheduler<br/>Bin-packing Algorithm<br/>15min Early Provision]
        ANSIBLE[⚙️ Ansible Service<br/>Infrastructure as Code<br/>Server Lifecycle]
        DNS[🌐 DNS Service<br/>Dynamic Domain Setup]

        SCHED --> ANSIBLE
        ANSIBLE --> DNS
    end

    subgraph "☁️ DigitalOcean Cloud"
        DO_API[🔧 DO API<br/>Droplet Management]
        DO_SPACES[📦 DO Spaces<br/>Video Storage<br/>recordings/org/date/]
        DO_DNS[🌍 DO DNS<br/>Domain Records]

        ANSIBLE --> DO_API
        DNS --> DO_DNS
    end

    subgraph "⚖️ Load Balancing"
        SCALELITE[🔀 Scalelite<br/>Meeting Distribution<br/>Server Health Check]
    end

    subgraph "🖥️ BBB Servers (Auto-Provisioned)"
        BBB_M[📺 BBB Medium<br/>8vCPU 16GB<br/>160 users<br/>Raw Transfer Only]
        BBB_L[📺 BBB Large<br/>16vCPU 32GB<br/>320 users<br/>Raw Transfer Only]
        BBB_XL[📺 BBB XL<br/>16vCPU 64GB<br/>640 users<br/>Raw Transfer Only]
    end

    subgraph "🎬 Processing Infrastructure"
        PROC_SERVER[�️ Processing Server<br/>Immediate Processing<br/>File Watcher + Queue<br/>Thread-Safe Processing]
        RAW_MONITOR[👁️ Raw File Monitor<br/>File System Watcher<br/>Polling Fallback]
        VIDEO_PROC[🔄 Video Processor<br/>bbb-record --rebuild<br/>MP4 Generation]
        UPLOAD[☁️ Cloud Uploader<br/>S3 Upload to Spaces<br/>Clean Path Structure]

        PROC_SERVER --> RAW_MONITOR
        RAW_MONITOR --> VIDEO_PROC
        VIDEO_PROC --> UPLOAD
        UPLOAD --> DO_SPACES
    end

    subgraph "📊 External Systems"
        HASURA[📈 Hasura GraphQL<br/>Lecture Schedule Data]
        USERS[👥 End Users<br/>Students & Teachers]
    end

    %% Main Data Flow
    HASURA -->|Daily Lecture<br/>Schedules| API
    API --> SCHED

    %% Infrastructure Provisioning
    ANSIBLE -.->|Deploy & Configure<br/>Auto SSL & Domain| BBB_M
    ANSIBLE -.->|Deploy & Configure<br/>Auto SSL & Domain| BBB_L
    ANSIBLE -.->|Deploy & Configure<br/>Auto SSL & Domain| BBB_XL
    ANSIBLE -.->|Register Servers| SCALELITE
    ANSIBLE -.->|Setup Processing| PROC_SERVER

    %% User Meeting Flow
    USERS -->|Join Meetings| SCALELITE
    SCALELITE -->|Distribute Load| BBB_M
    SCALELITE -->|Distribute Load| BBB_L
    SCALELITE -->|Distribute Load| BBB_XL

    %% Recording Flow (IMMEDIATE)
    BBB_M -.->|Meeting Ends<br/>Raw TAR Transfer<br/>post_archive.rb| PROC_SERVER
    BBB_L -.->|Meeting Ends<br/>Raw TAR Transfer<br/>post_archive.rb| PROC_SERVER
    BBB_XL -.->|Meeting Ends<br/>Raw TAR Transfer<br/>post_archive.rb| PROC_SERVER

    %% Server Lifecycle
    SCHED -.->|Auto Provision<br/>15min Before| DO_API
    SCHED -.->|Auto Decommission<br/>5min After| DO_API

    classDef control fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef automation fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef cloud fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef servers fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef processing fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef external fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef loadbalancer fill:#fff8e1,stroke:#ff8f00,stroke-width:2px

    class API,CACHE control
    class SCHED,ANSIBLE,DNS automation
    class DO_API,DO_SPACES,DO_DNS cloud
    class BBB_M,BBB_L,BBB_XL servers
    class PROC_SERVER,RAW_MONITOR,VIDEO_PROC,UPLOAD processing
    class HASURA,USERS external
    class SCALELITE loadbalancer
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Redis
- Ansible
- DigitalOcean API Token

### Installation

```bash
# Clone repository
git clone <repository-url>
cd bbb-manager

# Install NestJS dependencies
cd nest-app
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration
```

### Configuration

Create `.env` file in `nest-app/`:

```env
# DigitalOcean
DIGITALOCEAN_API_TOKEN=your_do_token
DIGITALOCEAN_DEFAULT_REGION=fra1

# Redis Cache
REDIS_URL=redis://localhost:6379

# Hasura GraphQL (for lecture data)
HASURA_GRAPHQL_ENDPOINT=https://your-hasura.com/v1/graphql
HASURA_GRAPHQL_ADMIN_SECRET=your_secret

# DNS & Domain
DNS_BASE_DOMAIN=your-domain.com

# Recording Storage
DO_SPACES_ACCESS_KEY=your_spaces_key
DO_SPACES_SECRET_KEY=your_spaces_secret
DO_SPACES_BUCKET=recordings-bucket
```

### Start Services

```bash
# Start Redis
redis-server

# Start BBB Manager
cd nest-app
npm run start:dev
```

## 📡 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/bbb-scheduler/trigger` | Manually trigger server scheduling |
| `GET` | `/bbb-scheduler/status` | Get current server status and operations |
| `GET` | `/bbb-scheduler/processing-status` | Get processing server status |
| `GET` | `/bbb-scheduler/processing-service-status` | Get immediate processing service status |
| `POST` | `/bbb-scheduler/restart-processing-service` | Restart immediate processing service |
| `GET` | `/cache-debug/monitor` | Real-time system monitoring |
| `GET` | `/cache-debug/servers` | List all managed servers |

## 🏗️ System Components

### NestJS Application (`nest-app/`)

**Core Modules:**
- **📅 BBB Scheduler**: Main orchestration service with bin-packing algorithm
- **🤖 Ansible Service**: Infrastructure automation and server lifecycle management
- **🌐 DNS Service**: Dynamic domain management for each server
- **💾 Cache Module**: Redis-based caching for lectures and server status
- **🔧 Configuration**: Environment-based configuration management

**Key Features:**
- Cron-based scheduling (every 5 minutes)
- Intelligent server allocation using bin-packing
- Real-time status tracking and health monitoring
- Graceful server decommissioning with meeting detection

### Ansible Automation (`bbb-ansible/`)

**Playbooks:**
- `bbb-provision.yml`: Complete BBB server setup with SSL
- `bbb-decommission.yml`: Clean server shutdown and removal
- `check-status.yml`: Health monitoring and status checks

**Roles:**
- **`bbb/`**: Complete BigBlueButton installation and configuration
- **`bbb_recording/`**: Raw file transfer setup (recording servers only)
- **`scalelite/`**: Load balancer integration and server registration

**Note**: Processing server is configured manually as a standalone server - no Ansible management needed.

**Roles:**
- **`bbb/`**: BigBlueButton installation and configuration
- **`scalelite/`**: Load balancer registration and management
- **`bbb_recording/`**: Recording workflow setup

**Note**: Processing server is configured manually as a standalone server.

## 🎬 Recording Workflow (Immediate Processing)

1. **Raw Recording**: BBB servers record meetings locally (no local processing)
2. **Immediate Transfer**: Post-archive hook transfers raw files immediately after meeting ends
3. **Immediate Processing**: Processing server detects new files and starts processing immediately
4. **Real-time Monitoring**: File system watcher + polling fallback ensures no files are missed
5. **Video Generation**: Raw recordings converted to MP4 videos in real-time
6. **Cloud Storage**: Final videos uploaded to DigitalOcean Spaces with clean path structure:
   ```
   recordings/organization/YYYY-MM-DD/meeting-id/video.mp4
   ```
7. **Cleanup**: Local recordings cleaned up automatically after successful processing

**⚡ Key Improvement**: Processing now starts **immediately** when recordings arrive, not daily at 6 PM!

## 📊 Server Types & Capacity

| Type | vCPUs | RAM | Capacity | Use Case |
|------|-------|-----|----------|----------|
| **Medium** | 8 | 16GB | 160 users | Standard lectures |
| **Large** | 16 | 32GB | 320 users | Large classes |
| **Extra Large** | 16 | 64GB | 640 users | Conferences |

## 🔧 Monitoring & Debugging

### Health Checks
```bash
# Check scheduler status
curl http://localhost:3000/bbb-scheduler/status

# Monitor cache and servers
curl http://localhost:3000/cache-debug/monitor

# View Redis statistics
curl http://localhost:3000/cache-debug/stats
```

### Server Management
```bash
# List active servers
curl http://localhost:3000/cache-debug/servers

# Check processing status
curl http://localhost:3000/bbb-scheduler/processing-status

# Trigger manual processing
curl -X POST http://localhost:3000/bbb-scheduler/process
```

## 🎯 Intelligent Scheduling

The system uses a sophisticated bin-packing algorithm:

1. **📈 Analysis**: Fetches lecture schedules from Hasura GraphQL
2. **⏰ Time Windows**: Creates time intervals based on concurrent participants
3. **📦 Bin Packing**: Optimally allocates servers to minimize count
4. **🕒 Just-in-Time**: Provisions servers 15 minutes before first lecture
5. **🧹 Auto Cleanup**: Decommissions servers 5 minutes after last use

## 🛠️ Development

### Project Structure
```
bbb-manager/
├── nest-app/           # NestJS application
│   ├── src/
│   │   ├── bbb/        # Scheduler service
│   │   ├── ansible/    # Infrastructure automation
│   │   ├── cache/      # Redis caching
│   │   ├── dns/        # Domain management
│   │   └── interfaces/ # TypeScript interfaces
│   └── package.json
└── bbb-ansible/        # Ansible automation
    ├── playbooks/      # Main playbooks
    ├── roles/          # Reusable roles
    └── inventory/      # Dynamic inventory
```

### Running Tests
```bash
cd nest-app
npm run test
npm run test:e2e
```

### Production Deployment
```bash
cd nest-app
npm run build
pm2 start dist/main.js --name bbb-manager
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
