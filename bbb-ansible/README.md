# BigBlueButton Ansible Automation

[![Ansible](https://img.shields.io/badge/Ansible-2.10+-blue.svg)](https://www.ansible.com/)
[![BBB Version](https://img.shields.io/badge/BigBlueButton-3.0-green.svg)](https://bigbluebutton.org/)
[![Ubuntu](https://img.shields.io/badge/Ubuntu-22.04-orange.svg)](https://ubuntu.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

Complete Ansible automation for **BigBlueButton 3.0** deployment with **Scalelite** load balancing and distributed recording processing.

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Users & Applications"
        U[👥 End Users]
        API[🔗 API Clients]
    end

    subgraph "Load Balancer"
        SL[⚖️ Scalelite<br/>bbb-lb.geerd.net<br/>Port 80/443]
    end

    subgraph "BigBlueButton Servers"
        BBB1[🖥️ BBB Medium<br/>160 users<br/>8 CPU / 16GB]
        BBB2[🖥️ BBB Large<br/>320 users<br/>16 CPU / 32GB]
        BBB3[🖥️ BBB Extra Large<br/>640 users<br/>16 CPU / 64GB]
    end

    subgraph "Recording Processing"
        PS[🎬 Processing Server<br/>5.189.175.46<br/>Raw → Video]
    end

    subgraph "Cloud Storage"
        DOS[☁️ DigitalOcean Spaces<br/>bbb-terraform bucket<br/>fra1 region]
    end

    subgraph "Automation"
        ANS[🤖 Ansible Controller<br/>Deploy & Configure]
    end

    %% User connections
    U --> SL
    API --> SL

    %% Load balancer distribution
    SL --> BBB1
    SL --> BBB2
    SL --> BBB3

    %% Recording workflow
    BBB1 -.->|SSH Transfer| PS
    BBB2 -.->|SSH Transfer| PS
    BBB3 -.->|SSH Transfer| PS
    PS -->|Upload| DOS

    %% Management
    ANS --> SL
    ANS --> BBB1
    ANS --> BBB2
    ANS --> BBB3
    ANS --> PS

    classDef users fill:#e3f2fd
    classDef loadbalancer fill:#e8f5e8
    classDef bbbserver fill:#fff3e0
    classDef processing fill:#fce4ec
    classDef storage fill:#f3e5f5
    classDef automation fill:#e1f5fe

    class U,API users
    class SL loadbalancer
    class BBB1,BBB2,BBB3 bbbserver
    class PS processing
    class DOS storage
    class ANS automation
```

## 🎯 What This Automation Does

### 🚀 **Server Provisioning**
- Installs BigBlueButton 3.0 on Ubuntu 22.04 servers
- Configures SSL certificates with Let's Encrypt
- Sets up firewall and security hardening
- Optimizes server capacity based on hardware type

### ⚖️ **Load Balancing**
- Deploys and configures Scalelite load balancer
- Automatically registers BBB servers with load balancer
- Enables/disables servers for maintenance
- Provides health monitoring and failover

### 📹 **Distributed Recording**
- Separates recording capture from video processing
- Automatically transfers raw recordings to processing servers
- Converts recordings to MP4 videos
- Uploads finished videos to DigitalOcean Spaces cloud storage

## 🛠️ Server Types & Capacities

| Type | Max Users | CPU | Memory | Use Case |
|------|-----------|-----|--------|----------|
| **medium** | 160 | 8 vCPUs | 16GB | Small to medium meetings |
| **large** | 320 | 16 vCPUs | 32GB | Large conferences |
| **extra_large** | 640 | 16 vCPUs | 64GB | Enterprise events |

## 🚀 Quick Start

### Prerequisites
- **Ansible 2.10+** installed on control machine
- **Ubuntu 22.04 LTS** target servers
- **Domain names** configured for your servers
- **DigitalOcean** account (for inventory and storage)

### 1. Environment Setup

```bash
# Install required Ansible collections
ansible-galaxy collection install community.general ansible.posix

# Configure environment variables
export DO_API_TOKEN="your_digitalocean_token"
export BBB_DOMAIN="yourdomain.com"
export SCALELITE_IP="your_scalelite_server_ip"
```

### 2. Configuration

Edit `group_vars/all.yml` with your settings:

```yaml
# Essential configuration
base_domain: "yourdomain.com"
email: "<EMAIL>"
organization: "your-org"

# BigBlueButton settings
bbb:
  version: "jammy-300"
  recording_enabled: true

# Scalelite load balancer
scalelite:
  enabled: true
  server_ip: "your_scalelite_ip"
  server_user: "root"
  ssh_password: "your_password"

# DigitalOcean Spaces for recordings
storage:
  bucket_name: "your-bucket"
  region: "fra1"
  access_key: "your_spaces_key"
  secret_key: "your_spaces_secret"

# Processing server for recordings
video_processing_server:
  enabled: true
  ip: "your_processing_server_ip"
  user: "root"
  password: "your_password"
```

### 3. Deploy Your Infrastructure

```bash
# Deploy BBB servers
ansible-playbook -i inventory/droplet.ini playbooks/bbb-provision.yml

# Deploy specific server type
ansible-playbook -i inventory/droplet.ini playbooks/bbb-provision.yml \
  --extra-vars "server_type=large"

# Note: Processing server is configured manually
```

## 📚 Available Playbooks

### 🎯 `bbb-provision.yml` - Main Server Deployment

**Purpose**: Complete BigBlueButton server installation and configuration

**What it does**:
- ✅ Validates system requirements (OS, memory, CPU, disk space)
- ✅ Installs BigBlueButton 3.0 with optimized settings
- ✅ Configures SSL certificates (Let's Encrypt)
- ✅ Sets up firewall and security hardening
- ✅ Configures server capacity based on type
- ✅ Registers server with Scalelite load balancer

**Usage**:
```bash
# Deploy all servers
ansible-playbook -i inventory/droplet.ini playbooks/bbb-provision.yml

# Deploy specific server type
ansible-playbook playbooks/bbb-provision.yml --extra-vars "server_type=large"

# Deploy to specific servers
ansible-playbook playbooks/bbb-provision.yml --limit "bbb-medium-*"
```

### ️ `bbb-decommission.yml` - Safe Server Removal

**Purpose**: Gracefully shutdown servers with data preservation

**What it does**:
- ✅ Checks for active meetings (blocks if found)
- ✅ Backs up recordings to cloud storage
- ✅ Unregisters server from Scalelite
- ✅ Performs clean shutdown

**Usage**:
```bash
# Safe decommission (protects active meetings)
ansible-playbook playbooks/bbb-decommission.yml --limit "server-to-remove"

# Force decommission (override active meetings)
ansible-playbook playbooks/bbb-decommission.yml \
  --extra-vars "force_decommission=true" --limit "server-to-remove"
```

## 🎭 Ansible Roles Explained

### 🔧 **bbb** - Core BigBlueButton Installation

**What it does**:
- System validation and preparation
- BigBlueButton 3.0 installation
- SSL certificate configuration
- Performance optimization based on server type
- Security hardening

**Key features**:
- Supports Ubuntu 22.04 LTS validation
- Automatic capacity configuration (160/320/640 users)
- Let's Encrypt SSL automation
- UFW firewall setup

### 📹 **bbb_recording** - Recording Transfer Setup

**What it does**:
- Configures post-meeting recording transfer
- Sets up SSH-based file transfer to processing servers
- Installs transfer scripts and automation

**How it works**:
1. Meeting ends → `post_archive.rb` hook triggered
2. Creates TAR archive of raw recording files
3. Transfers via SSH to processing server
4. Cleans up local files after successful transfer

### 🏭 **Processing Server** - Manual Setup

**Note**: The processing server is configured manually as a standalone server.
**Components**:
- Raw file processor daemon (monitors incoming files)
- BigBlueButton video processing pipeline
- Automated cloud storage upload to DigitalOcean Spaces

**How it works**:
1. Daemon monitors for incoming raw files
2. Extracts TAR archives automatically
3. Triggers BigBlueButton video processing
4. Uploads finished MP4 files to cloud storage

### ⚖️ **scalelite** - Load Balancer Integration

**What it does**:
- Registers BBB servers with Scalelite
- Manages server states (enable/disable)
- Handles server lifecycle events

**Key operations**:
- Automatic server registration after deployment
- Health monitoring integration
- Graceful server deregistration during maintenance

## 🔄 Recording Workflow Deep Dive

```mermaid
sequenceDiagram
    participant M as 👥 Meeting
    participant BBB as 🖥️ BBB Server
    participant PS as 🎬 Processing Server
    participant S3 as ☁️ DigitalOcean Spaces

    Note over M,S3: Complete Recording Workflow

    M->>BBB: Meeting starts & recording begins
    M->>BBB: Meeting ends
    BBB->>BBB: post_archive.rb hook triggered
    BBB->>BBB: Create TAR archive of raw files
    BBB->>PS: SSH transfer TAR to processing server
    BBB->>BBB: Clean up local raw files

    PS->>PS: Extract TAR archive
    PS->>PS: Run bbb-record --rebuild
    PS->>PS: Generate MP4 video files
    PS->>S3: Upload videos to Spaces bucket
    PS->>PS: Clean up processed files

    Note over BBB,S3: Raw files processed independently<br/>from live meetings
```

### Recording Components

1. **Raw Capture** (BBB Servers):
   - Records meeting audio, video, screen shares
   - Stores events.xml with meeting metadata
   - Creates raw files in `/var/bigbluebutton/recording/raw/`

2. **Transfer Process** (bbb_recording role):
   - `upload_raw_recording.rb` script triggers after meeting
   - Creates compressed TAR archive
   - Securely transfers via SSH to processing server
   - Logs all operations for troubleshooting

3. **Video Processing** (manual setup on standalone server):
   - `raw_processor.rb` daemon monitors for new files
   - Extracts archives and triggers BBB processing
   - Generates MP4 videos with synchronized audio/video
   - `upload_video.rb` uploads to DigitalOcean Spaces

## 🔍 Monitoring & Troubleshooting

### Important Log Files

| Component | Log Location | Purpose |
|-----------|--------------|---------|
| **BBB Installation** | `/var/log/bigbluebutton/bbb-install.log` | Installation progress and errors |
| **Recording Transfer** | `/var/log/bigbluebutton/raw_transfer.log` | SSH transfer operations |
| **Video Processing** | `/var/log/bigbluebutton/raw_processor.log` | Processing daemon activity |
| **Cloud Upload** | `/var/log/bigbluebutton/post_publish.log` | Spaces upload results |
| **Scalelite Operations** | `/var/log/bigbluebutton/scalelite.log` | Load balancer registration |

### Common Troubleshooting

#### 🔴 SSL Certificate Issues
```bash
# Check certificate status
sudo certbot certificates

# Force renewal
sudo certbot renew --force-renewal

# Verify domain resolution
dig +short your-server.yourdomain.com
```

#### 🔴 Recording Transfer Problems
```bash
# Check transfer logs
sudo tail -f /var/log/bigbluebutton/raw_transfer.log

# Test SSH connectivity
ssh -o StrictHostKeyChecking=no root@processing-server-ip

# Manual transfer test
sudo -u bigbluebutton /usr/local/bigbluebutton/core/scripts/post_archive/upload_raw_recording.rb -m test-meeting-id
```

#### 🔴 Scalelite Integration Issues
```bash
# Check registered servers
ssh root@scalelite-server "docker exec scalelite-api bundle exec rake servers"

# Re-register server
ansible-playbook playbooks/bbb-provision.yml --tags scalelite --limit your-server

# Test BBB API
curl -k https://your-server.yourdomain.com/bigbluebutton/api
```

### Health Checks

```bash
# Check BBB status
sudo bbb-conf --check

# Monitor system resources
htop
df -h
free -h

# View active recordings
sudo bbb-record --list

# Check meeting status
sudo bbb-conf --status
```

## 🔐 Security Features

- **Firewall Configuration**: Automated UFW setup with required ports only
- **SSL/TLS Encryption**: Let's Encrypt certificates with auto-renewal
- **SSH Security**: Key-based authentication for server communication
- **Access Control**: Service isolation and restrictive file permissions
- **Data Encryption**: HTTPS for all API calls, encrypted cloud storage

## 🚀 Production Deployment Tips

### High Availability Setup
```bash
# Deploy multiple server types for redundancy
ansible-playbook playbooks/bbb-provision.yml --extra-vars "server_type=medium"
ansible-playbook playbooks/bbb-provision.yml --extra-vars "server_type=large"
ansible-playbook playbooks/bbb-provision.yml --extra-vars "server_type=extra_large"
```

### Scaling Operations
```bash
# Add capacity during peak times
ansible-playbook playbooks/bbb-provision.yml --extra-vars "server_type=large server_count=2"

# Remove servers during low usage
ansible-playbook playbooks/bbb-decommission.yml --limit "bbb-medium-*"
```

### Maintenance Windows
```bash
# Disable server in load balancer for maintenance
ssh root@scalelite-server "docker exec scalelite-api bundle exec rake 'servers:disable[server-id]'"

# Re-enable after maintenance
ssh root@scalelite-server "docker exec scalelite-api bundle exec rake 'servers:enable[server-id]'"
```

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

1. **Test thoroughly** - All changes should be tested in development environment
2. **Document changes** - Update relevant README files and inline comments
3. **Follow conventions** - Use existing code style and naming patterns
4. **Security first** - Consider security implications of all changes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [BigBlueButton](https://bigbluebutton.org/) - Open-source web conferencing platform
- [Scalelite](https://github.com/blindsidenetworks/scalelite) - BBB load balancer
- [DigitalOcean](https://www.digitalocean.com/) - Cloud infrastructure provider
- Ansible community for automation best practices

---

**Built with ❤️ for scalable video conferencing**

For support: [<EMAIL>](mailto:<EMAIL>)
