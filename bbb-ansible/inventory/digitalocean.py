#!/usr/bin/env python3
"""
DigitalOcean dynamic inventory script for BigBlueButton servers.
This script fetches droplets from DigitalOcean API and creates an Ansible inventory.
"""

import json
import os
import sys
import re
import argparse
import urllib.request
import urllib.error
import ssl


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Dynamic inventory script for DigitalOcean droplets')
    group = parser.add_mutually_exclusive_group(required=False)
    group.add_argument('--list', action='store_true', help='List all hosts (default)')
    group.add_argument('--host', help='Get variables for a specific host')
    parser.add_argument('--tag', help='Filter droplets by tag', default='bbb')
    return parser.parse_args()


def get_droplets(api_token, tag=None):
    """Fetch droplets from DigitalOcean API, optionally filtered by tag"""
    headers = {
        'Authorization': f'Bearer {api_token}',
        'Content-Type': 'application/json'
    }

    url = 'https://api.digitalocean.com/v2/droplets'
    if tag:
        url += f'?tag_name={tag}'

    req = urllib.request.Request(url, headers=headers)

    try:
        # Create a context that doesn't verify SSL certificates
        context = ssl._create_unverified_context()

        # Add timeout to prevent hanging connections
        with urllib.request.urlopen(req, timeout=10, context=context) as response:
            data = json.loads(response.read().decode('utf-8'))
            return data.get('droplets', [])
    except urllib.error.HTTPError as e:
        sys.stderr.write(f"Error fetching droplets: {e.code} - {e.reason}\n")
        return []
    except urllib.error.URLError as e:
        sys.stderr.write(f"Error connecting to DigitalOcean API: {e.reason}\n")
        sys.stderr.write(f"Attempted to connect to URL: {url}\n")
        return []
    except json.JSONDecodeError as e:
        sys.stderr.write(f"Error parsing response from DigitalOcean API: {str(e)}\n")
        return []
    except Exception as e:
        sys.stderr.write(f"Unexpected error while connecting to DigitalOcean API: {str(e)}\n")
        return []


def determine_server_type(vcpus, memory_mb):
    """Determine BBB server type based on droplet specs"""
    if vcpus >= 16 and memory_mb >= 65536:  # 64GB
        return "extra_large"
    elif vcpus >= 16 and memory_mb >= 32768:  # 32GB
        return "large"
    else:
        return "medium"  # Default to medium for anything smaller


def estimate_capacity(server_type):
    """Estimate server capacity based on server type"""
    capacities = {
        "medium": 160,
        "large": 320,
        "extra_large": 640
    }
    return capacities.get(server_type, 160)


def get_droplet_ip(droplet):
    """Get the public IPv4 address of a droplet"""
    for network in droplet.get('networks', {}).get('v4', []):
        if network.get('type') == 'public':
            return network.get('ip_address')
    return None


def extract_server_id(hostname):
    """Extract server ID from BBB server name
    Handles formats like:
    - bbb11.geerd.net -> 11
    - bbb-medium-1744992266671 -> medium-1744992266671
    """
    # Try to match bbb11 pattern
    server_id_match = re.search(r'^bbb([0-9]+)', hostname)
    if server_id_match:
        return server_id_match.group(1)

    # Try to match bbb-{server_type}-id pattern
    server_id_match = re.search(r'^bbb-([a-zA-Z0-9-]+)', hostname)
    if server_id_match:
        return server_id_match.group(1)

    # Default to just using a number if we can extract it
    number_match = re.search(r'([0-9]+)', hostname)
    if number_match:
        return number_match.group(1)

    # Fallback to the hostname
    return hostname


def main():
    """Main function"""
    args = parse_args()

    # Get DigitalOcean API token from environment
    api_token = os.environ.get('DO_API_TOKEN')
    if not api_token:
        sys.stderr.write("Error: DO_API_TOKEN environment variable not set\n")
        return 1

    # Get Scalelite API details from environment
    scalelite_api_url = os.environ.get('SCALELITE_API_URL', 'https://bbb-lb.geerd.net/api')
    scalelite_api_secret = os.environ.get('SCALELITE_API_SECRET', '')
    base_domain = os.environ.get('BBB_BASE_DOMAIN', 'geerd.net')

    # Get tag from command line or environment variable
    tag = args.tag or os.environ.get('DO_TAG', 'bbb')

    # Initialize inventory
    inventory = {
        'bbb_servers': {
            'hosts': []
        },
        'all': {
            'children': ['bbb_servers']
        },
        '_meta': {
            'hostvars': {}
        }
    }

    # Handle --host argument
    if args.host:
        # For --host, we need to return variables for a specific host
        droplets = get_droplets(api_token)  # Don't filter by tag for specific host lookup
        for droplet in droplets:
            if droplet.get('name') == args.host:
                vcpus = droplet.get('vcpus', 0)
                memory_mb = droplet.get('memory', 0)
                server_type = determine_server_type(vcpus, memory_mb)
                server_capacity = estimate_capacity(server_type)
                ip_address = get_droplet_ip(droplet)
                droplet_name = droplet.get('name')
                server_id = extract_server_id(droplet_name)

                host_vars = {
                    'ansible_host': ip_address,
                    'ansible_user': 'root',  # Default user for DigitalOcean droplets
                    'ansible_ssh_private_key_file': os.path.expanduser('~/.ssh/id_rsa'),  # Default SSH key path
                    'server_id': server_id,
                    'server_type': server_type,
                    'server_capacity': server_capacity,
                    'scalelite_api_url': scalelite_api_url,
                    'scalelite_api_secret': scalelite_api_secret,
                    'base_domain': base_domain,
                    'bbb_domain': f"bbb{server_id}.{base_domain}",
                    'droplet_id': droplet.get('id'),
                    'region': droplet.get('region', {}).get('slug'),
                    'created_at': droplet.get('created_at'),
                    'tags': droplet.get('tags', [])
                }

                print(json.dumps(host_vars))
                return 0

        # If we get here, the host wasn't found
        print(json.dumps({}))
        return 0

    # Handle --list argument (default)
    droplets = get_droplets(api_token, tag)

    for droplet in droplets:
        # Only include active droplets
        if droplet.get('status') == 'active':
            droplet_name = droplet.get('name')
            vcpus = droplet.get('vcpus', 0)
            memory_mb = droplet.get('memory', 0)
            server_type = determine_server_type(vcpus, memory_mb)
            server_capacity = estimate_capacity(server_type)
            ip_address = get_droplet_ip(droplet)

            if ip_address:
                # Add to hosts list
                inventory['bbb_servers']['hosts'].append(droplet_name)

                # Extract server ID
                server_id = extract_server_id(droplet_name)

                # Add host variables
                inventory['_meta']['hostvars'][droplet_name] = {
                    'ansible_host': ip_address,
                    'ansible_user': 'root',  # Default user for DigitalOcean droplets
                    'ansible_ssh_private_key_file': os.path.expanduser('~/.ssh/id_rsa'),  # Default SSH key path
                    'server_id': server_id,
                    'server_type': server_type,
                    'server_capacity': server_capacity,
                    'scalelite_api_url': scalelite_api_url,
                    'scalelite_api_secret': scalelite_api_secret,
                    'base_domain': base_domain,
                    'bbb_domain': f"bbb{server_id}.{base_domain}",
                    'droplet_id': droplet.get('id'),
                    'region': droplet.get('region', {}).get('slug'),
                    'created_at': droplet.get('created_at'),
                    'tags': droplet.get('tags', [])
                }

    # Log the number of servers found
    sys.stderr.write(f"Found {len(inventory['bbb_servers']['hosts'])} droplets with tag '{tag}'\n")

    print(json.dumps(inventory))
    return 0


if __name__ == '__main__':
    sys.exit(main())
