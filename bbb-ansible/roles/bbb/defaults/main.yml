---
# Default variables for BBB role - based on group_vars/all.yml

# Installation defaults
bbb_install_script_url: "https://raw.githubusercontent.com/bigbluebutton/bbb-install/v3.0.x-release/bbb-install.sh"
bbb_install_script_path: "/tmp/bbb-install.sh"
bbb_installation_timeout: 3600  # 1 hour timeout for BBB installation

# Essential defaults (fallbacks if not defined in group_vars)
default_base_domain: "geerd.net"
default_email: "<EMAIL>"
default_bbb_version: "jammy-300"

# BigBlueButton configuration - these are just defaults, group_vars will override
bbb_version: "{{ bbb.version | default(default_bbb_version) }}"
bbb_recording_enabled: "{{ bbb.recording_enabled | default(true) }}"

# Domain and Email fallbacks - only used if not set in group_vars
bbb_base_domain: "{{ base_domain | default(default_base_domain) }}"
bbb_email: "{{ email | default(default_email) }}"

# SSL Configuration
ssl_use_letsencrypt: "{{ ssl.use_letsencrypt | default(true) }}"

# Server types and capacities (aligned with nest-app BBB scheduler)
server_types:
  medium:
    capacity: 160
    cpu: 8
    memory: 16384
    jvm_heap: "2G"
    kurento_min_port: 32768
    kurento_max_port: 65535
  large:
    capacity: 320
    cpu: 16
    memory: 32768
    jvm_heap: "4G"
    kurento_min_port: 32768
    kurento_max_port: 65535
  extra_large:
    capacity: 640
    cpu: 16
    memory: 65536
    jvm_heap: "8G"
    kurento_min_port: 32768
    kurento_max_port: 65535
