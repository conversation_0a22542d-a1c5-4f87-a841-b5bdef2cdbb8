# BigBlueButton (BBB) Ansible Role

A comprehensive Ansible role for installing and configuring BigBlueButton 3.0 servers on Ubuntu 22.04 LTS with production-ready settings.

## Overview

This role automates the complete deployment of BigBlueButton servers with:
- Pre-installation system checks and validation
- Automated BBB 3.0 installation with Let's Encrypt SSL
- Configuration optimization based on server capacity
- Integration with Scalelite load balancer
- Support for multiple server types (medium, large, extra_large)

## Requirements

### System Requirements
- **OS**: Ubuntu 22.04 LTS (Jammy)
- **Architecture**: x86_64 (64-bit)
- **Kernel**: Linux 5.x or higher
- **Memory**: Minimum 8GB RAM (16GB+ recommended for production)
- **CPU**: Minimum 4 vCPUs (8+ recommended for production)
- **Disk**: Minimum 50GB free space
- **Locale**: en_US.UTF-8

### Network Requirements
- Domain name pointing to server IP
- Firewall ports:
  - TCP: 22 (SSH), 80 (HTTP), 443 (HTTPS)
  - UDP: 16384-32768 (WebRTC media)
- IPv6 support (optional but recommended)

### Dependencies
- Docker (for LibreOffice document conversion)
- Let's Encrypt for SSL certificates

## Role Structure

```
roles/bbb/
├── defaults/main.yml      # Default variables and fallbacks
├── handlers/main.yml      # Service restart handlers
├── tasks/
│   ├── main.yml          # Main task orchestration
│   ├── pre_checks.yml    # System validation and setup
│   ├── install.yml       # BBB installation
│   └── configure.yml     # BBB configuration
└── README.md             # This documentation
```

## Server Types

The role supports three predefined server types aligned with the nest-app BBB scheduler:

| Type | Capacity | CPU | Memory | JVM Heap | Description |
|------|----------|-----|--------|----------|-------------|
| `medium` | 160 users | 8 vCPUs | 16GB | 2G | Standard deployment |
| `large` | 320 users | 16 vCPUs | 32GB | 4G | High-capacity deployment |
| `extra_large` | 640 users | 16 vCPUs | 64GB | 8G | Maximum capacity deployment |

## Variables

### Required Variables (from group_vars/all.yml)
```yaml
base_domain: "geerd.net"                    # Your domain
email: "<EMAIL>"         # Let's Encrypt email
bbb:
  version: "jammy-300"                      # BBB version
  recording_enabled: true                   # Enable recordings
```

### Server Configuration
```yaml
server:
  id: "bbb-server-01"                       # Server identifier
  type: "medium"                            # Server type (medium/large/extra_large)
  capacity: 160                             # Max concurrent users
```

### Optional Variables
```yaml
development_environment: false              # Relaxed requirements for dev
ssl:
  use_letsencrypt: true                     # Enable Let's Encrypt SSL
```

## Task Breakdown

### 1. Main Tasks (`tasks/main.yml`)
- Extracts server type from hostname if not defined
- Sets server variables and metadata
- Orchestrates the installation process

### 2. Pre-Installation Checks (`tasks/pre_checks.yml`)
- **System Validation**: Ubuntu 22.04, x86_64 architecture, kernel version
- **Resource Checks**: Memory, CPU, disk space requirements
- **Network Setup**: Hostname, DNS resolution, firewall configuration
- **Locale Configuration**: Sets en_US.UTF-8 locale
- **Common Packages**: Installs essential packages (curl, python3, etc.)
- **Security**: Configures UFW firewall with required ports

### 3. Installation (`tasks/install.yml`)
- Downloads official BBB installation script
- Checks if BBB is already installed
- Runs BBB installation with Let's Encrypt SSL
- Retrieves and stores BBB secret for API access

### 4. Configuration (`tasks/configure.yml`)
- Sets maximum users based on server type
- Configures HTTPS endpoints
- Optimizes settings for server capacity

## Usage Examples

### Basic Playbook
```yaml
---
- hosts: bbb_servers
  become: yes
  roles:
    - bbb
```

### With Custom Server Configuration
```yaml
---
- hosts: bbb_servers
  become: yes
  vars:
    server:
      id: "bbb-large-01"
      type: "large"
      capacity: 320
  roles:
    - bbb
```

### Development Environment
```yaml
---
- hosts: dev_servers
  become: yes
  vars:
    development_environment: true
  roles:
    - bbb
```

## Integration Points

### Scalelite Integration
After successful installation, servers can be registered with Scalelite using the `bbb_server_secret` fact.

### Recording Integration
Works with the `bbb_recording` role for advanced recording features and cloud storage.

### Video Processing
Integrates with the manually configured processing server for post-meeting video processing.

## Post-Installation

### Verify Installation
```bash
# Check BBB status
sudo bbb-conf --check

# Get BBB secret
sudo bbb-conf --secret

# Test BBB installation
sudo bbb-conf --test
```

### Access BBB
- Web interface: `https://your-server.domain.com/`
- API endpoint: `https://your-server.domain.com/bigbluebutton/api`
- Demos: `https://your-server.domain.com/demo`

## Troubleshooting

### Common Issues
1. **Domain Resolution**: Ensure DNS points to server IP before installation
2. **Firewall**: Verify UDP ports 16384-32768 are open for WebRTC
3. **Memory**: Ensure adequate RAM for chosen server type
4. **SSL**: Let's Encrypt requires valid domain and email

### Logs Location
- BBB logs: `/var/log/bigbluebutton/`
- Installation logs: `/var/log/bbb-install.log`
- Ansible logs: Check playbook output

## Security Notes

- Firewall is automatically configured with UFW
- Let's Encrypt SSL certificates are auto-renewed
- BBB secret is automatically generated and secured
- Non-essential services are disabled by default

## Maintenance

### Updates
Use BBB's built-in update mechanism:
```bash
sudo apt update && sudo apt upgrade
sudo bbb-conf --restart
```

### Monitoring
Monitor server resources and BBB metrics through the configured monitoring stack.
