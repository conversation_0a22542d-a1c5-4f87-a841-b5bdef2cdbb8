---
# BBB installation tasks
- name: Download BBB installation script
  get_url:
    url: "{{ bbb_install_script_url }}"
    dest: "{{ bbb_install_script_path }}"
    mode: '0755'
  register: download_script

- name: Check if BigBlueButton is already installed
  stat:
    path: /usr/share/bbb-web/WEB-INF/classes/bigbluebutton.properties
  register: bbb_installed

- name: Debug BBB installation variables
  debug:
    msg:
      - "BBB Version: {{ bbb.version | default(default_bbb_version) }}"
      - "Email: {{ email | default(default_email) }}"
      - "Base Domain: {{ base_domain | default(default_base_domain) }}"
      - "DNS Name (if provided): {{ dns_name | default('Not provided') }}"
      - "Server Name: {{ server.server_name }}"
      - "Final Domain: {{ dns_name | default(server.server_name) }}.{{ base_domain | default(default_base_domain) }}"
      - "Install Script: {{ bbb_install_script_path }}"
  when: not bbb_installed.stat.exists

- name: Install BigBlueButton with Let's Encrypt SSL (production-ready)
  shell: >
    {{ bbb_install_script_path }} -v {{ bbb.version | default(default_bbb_version) }}
    -s {{ dns_name | default(server.server_name) }}.{{ base_domain | default(default_base_domain) }}
    -e {{ email | default(default_email) }}
    -w
  args:
    creates: /var/www/bigbluebutton/client/conf/config.xml
  async: "{{ bbb_installation_timeout }}"
  poll: 30
  register: bbb_install
  when: not bbb_installed.stat.exists

- name: Get BBB secret
  shell: bbb-conf --secret | grep "Secret:" | awk '{print $2}'
  register: bbb_secret
  changed_when: false
  when: bbb_installed.stat.exists or bbb_install.finished is defined
  retries: 3
  delay: 10
  until: bbb_secret.stdout is defined and bbb_secret.stdout != ""

- name: Set BBB secret fact
  set_fact:
    bbb_server_secret: "{{ bbb_secret.stdout }}"
  when: bbb_secret.stdout is defined and bbb_secret.stdout != ""
