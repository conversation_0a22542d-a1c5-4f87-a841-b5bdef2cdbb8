---
# Pre-installation checks and common setup for BBB 3.0

# Common setup tasks (previously in common role)
- name: Update apt cache
  apt:
    update_cache: yes
    cache_valid_time: 3600
  register: apt_update
  retries: 3
  delay: 5
  until: apt_update is success

- name: Install common packages
  apt:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - gnupg-agent
      - software-properties-common
      - python3-pip
      - python3-setuptools
      - python3-apt
      - sshpass
    state: present
  register: apt_install
  retries: 3
  delay: 5
  until: apt_install is success

- name: Set hostname
  hostname:
    name: "{{ server.id }}"

- name: Add hostname to /etc/hosts (localhost only)
  lineinfile:
    path: /etc/hosts
    line: "127.0.0.1 localhost {{ server.id }}"
    regexp: '^127\.0\.0\.1'
    state: present

- name: Add public IP to /etc/hosts for domain name
  lineinfile:
    path: /etc/hosts
    line: "{{ ansible_host }} {{ server.server_name }}.{{ base_domain | default(default_base_domain) }} {{ server.server_name }}"
    regexp: "{{ ansible_host }}"
    state: present

- name: Configure timezone
  timezone:
    name: UTC

- name: Configure firewall for TCP ports
  ufw:
    rule: allow
    port: "{{ item }}"
    proto: tcp
  loop:
    - 22    # SSH
    - 80    # HTTP
    - 443   # HTTPS

- name: Configure firewall for UDP port range
  ufw:
    rule: allow
    port: 16384:32768
    proto: udp

- name: Enable UFW
  ufw:
    state: enabled
    policy: deny

# Original pre-installation checks
- name: Check Ubuntu version
  assert:
    that:
      - ansible_distribution == 'Ubuntu'
      - ansible_distribution_version == '22.04'
    fail_msg: "BigBlueButton 3.0 requires Ubuntu 22.04 LTS"

- name: Check architecture
  assert:
    that:
      - ansible_architecture == 'x86_64'
    fail_msg: "BigBlueButton 3.0 requires 64-bit architecture"

- name: Check kernel version
  shell: "uname -r | grep -o '^[0-9]'"
  register: kernel_version
  changed_when: false

- name: Verify kernel version
  assert:
    that:
      - kernel_version.stdout | int >= 5
    fail_msg: "BigBlueButton 3.0 requires Linux kernel 5.x or higher (found: {{ kernel_version.stdout }})"

- name: Check if server meets minimum requirements for production
  assert:
    that:
      - ansible_memtotal_mb >= 8192   # At least 8GB RAM
      - ansible_processor_vcpus >= 4   # At least 4 vCPUs
    fail_msg: "Server does not meet minimum requirements for BigBlueButton 3.0 in production (8GB RAM, 4 CPU cores)"
  when: not development_environment | default(false)

- name: Warn about server not meeting recommended requirements
  debug:
    msg: "WARNING: Server does not meet recommended requirements for BigBlueButton 3.0 in production (8GB RAM, 4 CPU cores). Current: {{ ansible_memtotal_mb }}MB RAM, {{ ansible_processor_vcpus }} vCPUs. Performance may be degraded."
  when:
    - not development_environment | default(false)
    - ansible_memtotal_mb < 8192 or ansible_processor_vcpus < 4

- name: Check if server meets minimum requirements for development
  assert:
    that:
      - ansible_memtotal_mb >= 8192  # At least 8GB RAM
      - ansible_processor_vcpus >= 4  # At least 4 vCPUs
    fail_msg: "Server does not meet minimum requirements for BigBlueButton 3.0 in development (8GB RAM, 4 CPU cores)"
  when: development_environment | default(false)

- name: Check if domain resolves to server IP
  command: "host {{ server.id }}.{{ base_domain | default(default_base_domain) }}"
  register: host_result
  failed_when: false
  changed_when: false

- name: Verify domain resolution
  debug:
    msg: "WARNING: Domain {{ server.id }}.{{ base_domain | default(default_base_domain) }} does not resolve to {{ ansible_host }}. SSL certificate generation may fail."
  when: ansible_host not in host_result.stdout

- name: Check disk space in GB
  shell: "df -BG / | awk 'NR==2 {print $4}' | sed 's/G//'"
  register: disk_space_gb
  changed_when: false

- name: Verify disk space for production
  assert:
    that:
      - disk_space_gb.stdout | int >= 50
    fail_msg: "BigBlueButton 3.0 recommends at least 50GB of free disk space for recordings in production"
  when: not development_environment | default(false) and bbb.recording_enabled | default(true)

- name: Verify disk space for production without recordings
  assert:
    that:
      - disk_space_gb.stdout | int >= 50
    fail_msg: "BigBlueButton 3.0 requires at least 50GB of free disk space when recordings are disabled"
  when: not development_environment | default(false) and not bbb.recording_enabled | default(true)

- name: Verify disk space for development
  assert:
    that:
      - disk_space_gb.stdout | int >= 50
    fail_msg: "BigBlueButton 3.0 requires at least 50GB of free disk space for development"
  when: development_environment | default(false)

- name: Ensure en_US.UTF-8 locale is generated
  locale_gen:
    name: en_US.UTF-8
    state: present

- name: Set locale to en_US.UTF-8
  command: update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8
  changed_when: true

- name: Check locale configuration
  command: cat /etc/default/locale
  register: locale_result
  changed_when: false

- name: Verify locale is en_US.UTF-8
  assert:
    that:
      - '"LANG=en_US.UTF-8" in locale_result.stdout'
    fail_msg: "BigBlueButton 3.0 requires locale set to en_US.UTF-8"

- name: Set systemd environment LANG to en_US.UTF-8
  command: systemctl set-environment LANG=en_US.UTF-8
  changed_when: true

- name: Check systemd environment
  command: systemctl show-environment
  register: systemd_env
  changed_when: false

- name: Verify systemd LANG is en_US.UTF-8
  assert:
    that:
      - '"LANG=en_US.UTF-8" in systemd_env.stdout'
    fail_msg: "BigBlueButton 3.0 requires systemd environment LANG set to en_US.UTF-8"

- name: Check IPv6 support
  shell: "ip addr | grep -c 'inet6'"
  register: ipv6_support
  changed_when: false

- name: Warn about missing IPv6 support
  debug:
    msg: "WARNING: IPv6 support not detected. You will need to modify FreeSWITCH configuration after installation."
  when: ipv6_support.stdout | int == 0

- name: Check if Docker is installed
  command: which docker
  register: docker_check
  failed_when: false
  changed_when: false

- name: Warn about missing Docker
  debug:
    msg: "WARNING: Docker is not installed. BigBlueButton 3.0 requires Docker for LibreOffice document conversion."
  when: docker_check.rc != 0

- name: Check firewall status for TCP ports 80 and 443
  shell: "ufw status | grep -E '(80|443)'"
  register: firewall_tcp_ports
  failed_when: false
  changed_when: false

- name: Warn about firewall TCP ports
  debug:
    msg: "WARNING: TCP ports 80 and 443 may not be properly configured in the firewall."
  when: "'80/tcp' not in firewall_tcp_ports.stdout or '443/tcp' not in firewall_tcp_ports.stdout"

- name: Check firewall status for UDP port range
  shell: "ufw status | grep '16384:32768/udp'"
  register: firewall_udp_ports
  failed_when: false
  changed_when: false

- name: Warn about firewall UDP port range
  debug:
    msg: "WARNING: UDP port range 16384-32768 may not be properly configured in the firewall."
  when: firewall_udp_ports.rc != 0
