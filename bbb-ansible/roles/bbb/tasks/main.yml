---
# Main tasks for BBB role

# Set server variables if not already defined (for role portability)
- name: Extract server type from hostname
  set_fact:
    extracted_server_type: "{{ inventory_hostname | regex_search('bbb-([a-z]+)', '\\1') | first | default('medium') }}"
  when: server is not defined

- name: Set server variables
  set_fact:
    server:
      id: "{{ server_id | default(inventory_hostname) }}"
      type: "{{ server_type | default(extracted_server_type) }}"
      capacity: "{{ server_capacity | default(server_types[server_type | default(extracted_server_type)].capacity) }}"
      full_id: "{{ 'bbb-' + server_id if server_id is defined and not server_id.startswith('bbb-') else server_id | default(inventory_hostname) }}"
      server_name: "{{ server_id | default(inventory_hostname) }}"
  when: server is not defined

- name: Include pre-installation checks and common setup
  include_tasks: pre_checks.yml

- name: Include BBB installation tasks
  include_tasks: install.yml

- name: Include BBB configuration tasks
  include_tasks: configure.yml

# Recording configuration is now handled by the bbb_recording role
# Regular BBB servers only transfer raw files - no local processing!
