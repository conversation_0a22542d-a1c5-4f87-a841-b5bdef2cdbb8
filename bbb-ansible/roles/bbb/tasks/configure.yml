---
# BBB configuration tasks
- name: Set user limits based on server size
  set_fact:
    max_users: "{{ {'medium': 160, 'large': 320, 'extra_large': 640}[server.type] | default(160) }}"

- name: Configure BBB maximum users
  lineinfile:
    path: /usr/share/bbb-web/WEB-INF/classes/bigbluebutton.properties
    regexp: '^defaultMaxUsers='
    line: 'defaultMaxUsers={{ max_users }}'
    state: present
  notify: restart bbb services

- name: Configure BBB maximum participants
  lineinfile:
    path: /usr/share/bbb-web/WEB-INF/classes/bigbluebutton.properties
    regexp: '^maxParticipants='
    line: 'maxParticipants={{ max_users }}'
    state: present
  notify: restart bbb services

- name: Configure BBB to use HTTPS
  lineinfile:
    path: /usr/share/bbb-web/WEB-INF/classes/bigbluebutton.properties
    regexp: '^bigbluebutton\.web\.serverURL='
    line: 'bigbluebutton.web.serverURL=https://{{ server.server_name }}.{{ base_domain | default(default_base_domain) }}'
    state: present
  notify: restart bbb services

- name: Ensure BBB configuration is applied
  meta: flush_handlers

# Recording configuration is handled by bbb_recording role
