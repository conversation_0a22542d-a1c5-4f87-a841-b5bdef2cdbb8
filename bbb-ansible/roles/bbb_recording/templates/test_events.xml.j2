<?xml version="1.0" encoding="UTF-8"?>
<recording>
  <metadata>
    <meetingId>{{ meeting_id }}</meetingId>
    <meetingName>{{ meeting_name }}</meetingName>
    <organization>{{ organization | default('geerd') }}</organization>
    <startTime>{{ ansible_date_time.epoch }}000</startTime>
    <endTime>{{ (ansible_date_time.epoch | int + 300) }}000</endTime>
    <participants>1</participants>
    <rawSize>1024</rawSize>
  </metadata>
  <events>
    <event timestamp="{{ ansible_date_time.epoch }}000" module="PARTICIPANT" eventname="ParticipantJoinEvent">
      <userId>test-user-{{ ansible_date_time.epoch }}</userId>
      <externalUserId>test-user</externalUserId>
      <name>Test User</name>
      <role>MODERATOR</role>
    </event>
    <event timestamp="{{ (ansible_date_time.epoch | int + 300) }}000" module="PARTICIPANT" eventname="ParticipantLeftEvent">
      <userId>test-user-{{ ansible_date_time.epoch }}</userId>
    </event>
  </events>
</recording>
