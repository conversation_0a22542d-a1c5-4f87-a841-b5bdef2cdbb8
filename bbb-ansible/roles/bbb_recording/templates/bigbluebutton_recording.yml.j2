# BigBlueButton Recording Server Configuration
# This configuration is optimized for RAW CAPTURE + TRANSFER workflow
# Generated by Ansible bbb_recording role

bbb_version: '3.0.9'

# Source directories for different recording types
raw_audio_src: /var/freeswitch/meetings
mediasoup_video_src: /var/mediasoup/recordings
mediasoup_screenshare_src: /var/mediasoup/screenshare
webrtc_recorder_video_src: /var/lib/bbb-webrtc-recorder/recordings
webrtc_recorder_screenshare_src: /var/lib/bbb-webrtc-recorder/screenshare
webrtc_recorder_audio_src: /var/lib/bbb-webrtc-recorder/audio
raw_deskshare_src: /var/bigbluebutton/deskshare
raw_presentation_src: /var/bigbluebutton

# Notes configuration
notes_endpoint: http://127.0.0.1:9002/p
notes_formats:
  - etherpad
  - html
  - pdf

# Redis configuration
redis_host: 127.0.0.1
redis_port: 6379

# Recording status tracking
store_recording_status: false

# Chat anonymization settings
anonymize_chat: false
anonymize_chat_moderators: false
show_moderator_viewpoint: false

# CRITICAL: Recording Server Workflow
# This configuration ensures:
# 1. Raw files are captured during meetings (MUST enable video/presentation)
# 2. Files are archived and transferred to processing server
# 3. NO local video processing (saves resources)
# 4. Post-archive hooks trigger file transfer

steps:
  archive: 'sanity'
  sanity: 'captions'
  captions: 'post_archive'

# Directory configuration
log_dir: /var/log/bigbluebutton
events_dir: /var/bigbluebutton/events
recording_dir: /var/bigbluebutton/recording
published_dir: /var/bigbluebutton/published
captions_dir: /var/bigbluebutton/captions

# Playback configuration (not used on recording servers)
playback_host: {{ ansible_fqdn }}
playback_protocol: https
