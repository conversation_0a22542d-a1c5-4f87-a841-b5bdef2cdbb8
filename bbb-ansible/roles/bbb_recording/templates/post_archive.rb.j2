#!/usr/bin/env ruby
# BBB Post-Archive Hook - Transfer RAW files
# Generated by Ansible - bbb_recording role

require 'logger'
require 'fileutils'

# Setup logging
log_dir = '/var/log/bigbluebutton'
FileUtils.mkdir_p(log_dir) unless Dir.exist?(log_dir)
logger = Logger.new("#{log_dir}/post_archive.log", 'weekly')
logger.level = Logger::INFO

# Parse BBB's meeting_id argument
meeting_id = nil
if ARGV[0] == '-m' && ARGV[1]
  meeting_id = ARGV[1]
elsif ARGV[0] && ARGV[0] != '-m'
  meeting_id = ARGV[0]
end

if meeting_id.nil? || meeting_id.empty?
  logger.error("No meeting ID provided")
  puts "Error: No meeting ID provided"
  exit 1
end

# Validate meeting ID format for security
unless meeting_id.match?(/\A[a-zA-Z0-9_-]+\z/)
  logger.error("Invalid meeting ID format: #{meeting_id}")
  puts "Error: Invalid meeting ID format"
  exit 1
end

logger.info("Post-archive called for meeting: #{meeting_id}")

# Call our transfer script
transfer_script = '/usr/local/bigbluebutton/core/scripts/post_archive/upload_raw_recording.rb'

if File.exist?(transfer_script)
  logger.info("Transferring raw files for meeting: #{meeting_id}")

  # Execute transfer script with proper error handling
  start_time = Time.now
  success = system("#{transfer_script} -m '#{meeting_id}'")
  duration = Time.now - start_time

  if success
    logger.info("Transfer completed successfully for #{meeting_id} in #{duration.round(2)} seconds")
  else
    logger.error("Transfer failed for #{meeting_id} after #{duration.round(2)} seconds")
    exit 1
  end
else
  logger.error("Transfer script not found: #{transfer_script}")
  puts "Error: Transfer script not found"
  exit 1
end

logger.info("Post-archive processing completed for #{meeting_id}")
exit 0
