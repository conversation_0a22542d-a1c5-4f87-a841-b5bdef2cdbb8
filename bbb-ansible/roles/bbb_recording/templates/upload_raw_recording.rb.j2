#!/usr/bin/env ruby
# BBB RAW Recording Transfer Script - Simple and Clean
# Only transfers RAW recordings to processing server, no local processing

require 'fileutils'
require 'logger'
require 'optparse'

# Parse command line arguments - B<PERSON> calls this with -m meeting_id
meeting_id = nil
OptionParser.new do |opts|
  opts.on('-m', '--meeting-id MEETING_ID', 'Meeting ID to process') do |id|
    meeting_id = id
  end
end.parse!

# Fallback to positional argument if -m not used
meeting_id ||= ARGV[0]

if meeting_id.nil? || meeting_id.empty?
  puts "Usage: #{$0} -m <meeting_id> or #{$0} <meeting_id>"
  exit(1)
end

# Setup logging
log_dir = '/var/log/bigbluebutton'
FileUtils.mkdir_p(log_dir) unless Dir.exist?(log_dir)
logger = Logger.new("#{log_dir}/raw_transfer.log", 'weekly')
logger.level = Logger::INFO

logger.info("Starting RAW transfer for meeting: #{meeting_id}")

# RAW recording directory
raw_dir = "/var/bigbluebutton/recording/raw/#{meeting_id}"

# Wait for raw directory to be created (<PERSON><PERSON> might still be writing)
max_wait = 30
wait_count = 0
until Dir.exist?(raw_dir) || wait_count >= max_wait
  logger.info("Waiting for raw directory to be created: #{raw_dir}")
  sleep(1)
  wait_count += 1
end

unless Dir.exist?(raw_dir)
  logger.error("RAW recording directory not found after #{max_wait}s: #{raw_dir}")
  exit(1)
end

logger.info("Found RAW recording: #{raw_dir}")

# Create tar archive with _raw suffix (required by processing server)
tar_file = "/tmp/#{meeting_id}_raw.tar.gz"

begin
  # Wait for files to be written (BBB might still be writing)
  stable_count = 0
  last_file_count = 0

  5.times do
    raw_files = Dir.glob("#{raw_dir}/**/*").select { |f| File.file?(f) }
    current_count = raw_files.length

    if current_count == last_file_count && current_count > 0
      stable_count += 1
    else
      stable_count = 0
    end

    last_file_count = current_count
    logger.info("Raw directory scan: #{current_count} files (stability: #{stable_count}/3)")

    break if stable_count >= 3
    sleep(2)
  end

  # Final file verification
  raw_files = Dir.glob("#{raw_dir}/**/*").select { |f| File.file?(f) }
  if raw_files.empty?
    logger.error("No files found in raw directory: #{raw_dir}")
    logger.error("Directory contents: #{Dir.glob("#{raw_dir}/**/*")}")
    exit(1)
  end

  logger.info("Found #{raw_files.length} files in raw directory")

  # Log file details for debugging
  raw_files.first(5).each do |file|
    size = File.size(file)
    logger.info("  - #{File.basename(file)}: #{size} bytes")
  end

  # Create tar archive of raw files with proper compression
  tar_cmd = "tar -czf '#{tar_file}' -C '/var/bigbluebutton/recording/raw' '#{meeting_id}'"
  logger.info("Creating archive: #{tar_cmd}")

  unless system(tar_cmd)
    logger.error("Failed to create archive")
    exit(1)
  end

  # Verify archive was created and has content
  unless File.exist?(tar_file) && File.size(tar_file) > 0
    logger.error("Archive creation failed or archive is empty")
    exit(1)
  end

  logger.info("Created archive: #{tar_file} (#{File.size(tar_file)} bytes)")

  # Transfer to processing server
  tar_filename = File.basename(tar_file)
  {% if video_processing_server.ssh_key_path is defined and video_processing_server.ssh_key_path != "" %}
  transfer_cmd = "scp -o StrictHostKeyChecking=no -o ConnectTimeout=30 -i {{ video_processing_server.ssh_key_path }} '#{tar_file}' '{{ video_processing_server.user }}@{{ video_processing_server.ip }}:{{ video_processing_server.transfer_path }}/'"
  {% else %}
  transfer_cmd = "sshpass -p '{{ video_processing_server.password }}' scp -o StrictHostKeyChecking=no -o ConnectTimeout=30 '#{tar_file}' '{{ video_processing_server.user }}@{{ video_processing_server.ip }}:{{ video_processing_server.transfer_path }}/'"
  {% endif %}

  logger.info("Transferring #{tar_filename} to processing server {{ video_processing_server.ip }}...")
  logger.info("Transfer command: #{transfer_cmd.gsub(/password='[^']*'/, "password='***'")}")

  unless system(transfer_cmd)
    logger.error("Failed to transfer file to processing server")
    exit(1)
  end

  logger.info("Successfully transferred #{tar_filename} to processing server")

  # Verify transfer succeeded by checking file exists and has correct size on remote server
  {% if video_processing_server.ssh_key_path is defined and video_processing_server.ssh_key_path != "" %}
  verify_cmd = "ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 -i {{ video_processing_server.ssh_key_path }} {{ video_processing_server.user }}@{{ video_processing_server.ip }} 'ls -la {{ video_processing_server.transfer_path }}/#{tar_filename}'"
  {% else %}
  verify_cmd = "sshpass -p '{{ video_processing_server.password }}' ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 {{ video_processing_server.user }}@{{ video_processing_server.ip }} 'ls -la {{ video_processing_server.transfer_path }}/#{tar_filename}'"
  {% endif %}

  logger.info("Verifying transfer...")
  verify_output = `#{verify_cmd} 2>&1`

  if $?.success? && verify_output.include?(tar_filename)
    logger.info("Transfer verified successfully on processing server")
    logger.info("Remote file info: #{verify_output.strip}")
  else
    logger.error("Transfer verification failed - file not found or inaccessible on processing server")
    logger.error("Verification output: #{verify_output}")
    exit(1)
  end

  # Optional: Log notification to processing server
  {% if video_processing_server.trigger_processing | default(true) %}
  logger.info("Processing server will detect and process file automatically")
  {% endif %}

rescue => e
  logger.error("Transfer failed: #{e.message}")
  logger.error("Backtrace: #{e.backtrace.join("\n")}")
  exit(1)
ensure
  # Clean up temporary tar file
  if File.exist?(tar_file)
    FileUtils.rm_f(tar_file)
    logger.info("Cleaned up temporary tar file: #{tar_file}")
  end
end

logger.info("RAW transfer completed successfully for meeting: #{meeting_id}")
logger.info("Processing server will automatically detect and process the file")
exit(0)
