---
# Transfer raw recording files to processing server when meeting ends

- name: Verify B<PERSON> is installed
  stat:
    path: /usr/bin/bbb-record
  register: bbb_installed
  become: true

- name: Fail if BBB not installed
  fail:
    msg: "BigBlueButton not installed. Run bbb role first."
  when: not bbb_installed.stat.exists

- name: Install transfer dependencies
  apt:
    name:
      - sshpass
    state: present
  become: true

- name: Create post-archive directory
  file:
    path: /usr/local/bigbluebutton/core/scripts/post_archive
    state: directory
    owner: bigbluebutton
    group: bigbluebutton
    mode: '0755'
  become: true

- name: Deploy transfer script
  template:
    src: upload_raw_recording.rb.j2
    dest: /usr/local/bigbluebutton/core/scripts/post_archive/upload_raw_recording.rb
    owner: bigbluebutton
    group: bigbluebutton
    mode: '0755'
  become: true

- name: Ensure post_archive directory exists
  file:
    path: /usr/local/bigbluebutton/core/scripts/post_archive
    state: directory
    owner: bigbluebutton
    group: bigbluebutton
    mode: '0755'
  become: true

- name: Deploy BBB post-archive hook script
  template:
    src: post_archive.rb.j2
    dest: /usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb
    owner: bigbluebutton
    group: bigbluebutton
    mode: '0755'
  become: true

- name: Verify post-archive hook deployment
  stat:
    path: /usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb
  register: post_archive_hook

- name: Display post-archive hook status
  debug:
    msg:
      - "Post-archive hook deployment status:"
      - "File exists: {{ post_archive_hook.stat.exists }}"
      - "File path: /usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb"
      - "Owner: {{ post_archive_hook.stat.pw_name | default('unknown') }}"
      - "Permissions: {{ post_archive_hook.stat.mode | default('unknown') }}"

- name: Test post-archive hook syntax
  shell: "ruby -c /usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb"
  register: syntax_check
  changed_when: false
  become: true

- name: Display syntax check result
  debug:
    msg: "Post-archive hook syntax check: {{ syntax_check.stdout | default('OK') }}"

- name: Enable BBB recording globally
  shell: bbb-record --enable
  become: true
  changed_when: false

- name: Ensure BBB recording properties are configured for raw capture
  lineinfile:
    path: /usr/share/bbb-web/WEB-INF/classes/bigbluebutton.properties
    regexp: "{{ item.regexp }}"
    line: "{{ item.line }}"
    state: present
  loop:
    - { regexp: '^allowStartStopRecording=', line: 'allowStartStopRecording=true' }
    - { regexp: '^autoStartRecording=', line: 'autoStartRecording=false' }
    - { regexp: '^recordingEnabled=', line: 'recordingEnabled=true' }
  become: true
  notify: restart bbb services

- name: Deploy complete BBB YAML configuration for recording servers
  template:
    src: bigbluebutton_recording.yml.j2
    dest: /usr/local/bigbluebutton/core/scripts/bigbluebutton.yml
    owner: bigbluebutton
    group: bigbluebutton
    mode: '0644'
    backup: yes
  become: true
  notify: restart bbb services

- name: Ensure raw recording directory exists with correct permissions
  file:
    path: /var/bigbluebutton/recording/raw
    state: directory
    owner: bigbluebutton
    group: bigbluebutton
    mode: '0755'
    recurse: yes
  become: true

- name: Enable and start BBB recording services (CRITICAL for raw file creation)
  systemd:
    name: "{{ item }}"
    state: started
    enabled: yes
  loop:
    - bbb-rap-resque-worker
    - bbb-rap-starter
  become: true

- name: Keep video/presentation processing enabled for raw capture
  shell: "{{ item }}"
  loop:
    - bbb-record --enable video
    - bbb-record --enable presentation
  become: true
  changed_when: false
  ignore_errors: true

- name: Validate BBB YAML configuration syntax
  shell: |
    ruby -e "require 'yaml'; YAML.load_file('/usr/local/bigbluebutton/core/scripts/bigbluebutton.yml')"
  register: yaml_validation
  become: true
  changed_when: false
  failed_when: yaml_validation.rc != 0

- name: Display YAML validation result
  debug:
    msg: "BBB YAML configuration is valid"
  when: yaml_validation.rc == 0

- name: Verify BBB recording services are running
  systemd:
    name: "{{ item }}"
  register: service_status
  loop:
    - bbb-rap-resque-worker
    - bbb-rap-starter
  become: true

- name: Display recording server configuration
  debug:
    msg:
      - "🎯 BBB Recording Server configured for RAW CAPTURE + TRANSFER:"
      - "✅ Recording globally enabled: bbb-record --enable"
      - "✅ Raw file capture: ENABLED (video/presentation workers running)"
      - "✅ Recording services: {{ service_status.results | map(attribute='status.ActiveState') | join(', ') }}"
      - "✅ Raw file transfer: ENABLED"
      - "📁 Post-archive hook: /usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb"
      - "📡 Processing server: {{ video_processing_server.ip }}"
      - "📁 Transfer path: {{ video_processing_server.transfer_path }}"
      - "📜 Transfer script: /usr/local/bigbluebutton/core/scripts/post_archive/upload_raw_recording.rb"
      - ""
      - "🚀 BBB will now capture raw files during meetings and transfer them!"
      - "⚠️  Local processing disabled - only raw capture and transfer"

- name: List post-archive scripts that BBB will execute
  find:
    paths: /usr/local/bigbluebutton/core/scripts/post_archive
    patterns: "*.rb"
    file_type: file
  register: post_scripts
  become: true

- name: Display post-archive scripts
  debug:
    msg:
      - "Post-archive scripts that will be executed (in alphabetical order):"
      - "{{ post_scripts.files | map(attribute='path') | sort | list }}"
      - "Total scripts: {{ post_scripts.files | length }}"

- name: Verify BBB recording configuration is ready
  shell: |
    echo "🔍 BBB Recording Status Check:"
    echo "✅ Recording enabled: $(bbb-record --list | grep -q 'video.*enabled' && echo 'YES' || echo 'NO')"
    echo "✅ Worker service: $(systemctl is-active bbb-rap-resque-worker)"
    echo "✅ Raw directory: $(test -d /var/bigbluebutton/recording/raw && echo 'EXISTS' || echo 'MISSING')"
    echo "✅ Post-archive hook: $(test -f /usr/local/bigbluebutton/core/scripts/post_archive/01-raw-transfer.rb && echo 'DEPLOYED' || echo 'MISSING')"
  register: recording_status_check
  become: true
  changed_when: false

- name: Display BBB recording readiness
  debug:
    msg: "{{ recording_status_check.stdout_lines }}"
