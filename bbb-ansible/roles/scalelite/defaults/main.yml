---
# Default variables for Scalelite role - based on group_vars/all.yml

# Default base domain and server settings (fallbacks)
default_base_domain: "geerd.net"
default_server_user: "root"

# Scalelite configuration - defaults only, group_vars will override
scalelite_enabled: "{{ scalelite.enabled | default(false) }}"
scalelite_server_ip: "{{ scalelite.server_ip | default('**************') }}"
scalelite_ssh_password: "{{ scalelite.ssh_password | default('') }}"
scalelite_server_user: "{{ scalelite.server_user | default(default_server_user) }}"

# Domain construction - uses group_vars or fallback
scalelite_domain: "bbb-lb.{{ base_domain | default(default_base_domain) }}"
scalelite_api_url: "https://bbb-lb.{{ base_domain | default(default_base_domain) }}/api"
scalelite_frontend_url: "https://bbb-lb.{{ base_domain | default(default_base_domain) }}"

# Default action for Scalelite operations
scalelite_action: "register"

# Server configuration - no recursive reference
server_hostname: "{{ server_id | default(inventory_hostname) }}"
