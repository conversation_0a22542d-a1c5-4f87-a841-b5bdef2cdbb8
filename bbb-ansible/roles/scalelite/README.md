# Scalelite Load Balancer Role

## Overview

The Scalelite role manages BigBlueButton server registration with the Scalelite load balancer. It automatically registers/unregisters BBB servers and provides server management capabilities.

## What it Does

- **Registers** BBB servers with Scalelite load balancer
- **Unregisters** servers when decommissioning
- **Manages** server states (enable/disable)
- **Monitors** server health and availability

## Configuration

**Required Variables** (set in `group_vars/all.yml`):
```yaml
scalelite:
  enabled: true
  server_ip: "**************"
  server_user: "root"
  ssh_password: "---"
```

## Manual Server Management

### Connect to Scalelite Server
```bash
ssh root@**************
```

### List All Servers
```bash
docker exec -it scalelite-api bundle exec rake servers
```

### Add New BBB Server
```bash
docker exec -it scalelite-api bundle exec rake "servers:add[https://bbb.example.com/bigbluebutton/api,server-secret-here]"
```

### Enable Server
```bash
docker exec -it scalelite-api bundle exec rake "servers:enable[server-id]"
```

### Disable Server
```bash
docker exec -it scalelite-api bundle exec rake "servers:disable[server-id]"
```

### Remove Server
```bash
docker exec -it scalelite-api bundle exec rake "servers:remove[server-id]"
```

## Server Management Script

Your Scalelite server has a management script at `/root/manage-servers.sh`:

```bash
# Add server
./manage-servers.sh add https://bbb.example.com/bigbluebutton/api server-secret

# List all servers
./manage-servers.sh list

# Enable server by ID
./manage-servers.sh enable server-id

# Disable server by ID
./manage-servers.sh disable server-id

# Remove server by ID
./manage-servers.sh remove server-id
```

## Server States

- **📗 enabled**: Server accepts new meetings
- **📙 disabled**: Server drains existing meetings, no new ones
- **📕 offline**: Server is unreachable
- **⚪ cordoned**: Server is manually excluded

## Automated Registration

When using the Ansible playbooks:

### During Provisioning
```yaml
# Automatically registers server when BBB is deployed
- hosts: bbb_servers
  roles:
    - bbb
    - scalelite  # Registers server after BBB setup
```

### During Decommissioning
```yaml
# Automatically unregisters before destroying server
- hosts: bbb_servers
  roles:
    - scalelite  # Unregisters server before destruction
```

## Troubleshooting

### Check Server Status
```bash
# SSH to Scalelite server
ssh root@**************

# Check all servers
docker exec -it scalelite-api bundle exec rake servers

# Check specific server health
docker exec -it scalelite-api bundle exec rake "servers:check[server-id]"
```

### Common Issues

**Server shows as offline:**
- Check BBB server is running: `sudo bbb-conf --check`
- Verify API endpoint is accessible
- Check firewall settings

**Registration fails:**
- Verify server secret matches BBB server
- Check Scalelite can reach BBB server URL
- Ensure API endpoint format is correct

### Log Files
```bash
# Scalelite logs
docker logs scalelite-api
docker logs scalelite-nginx

# BBB server logs
tail -f /var/log/bigbluebutton/bbb-web.log
```

## Key Files

**Ansible Tasks:**
- `tasks/main.yml` - Main registration logic
- `tasks/register_server.yml` - Server registration
- `tasks/unregister_server.yml` - Server removal

**Configuration:**
- `defaults/main.yml` - Default variables
- `group_vars/all.yml` - Global Scalelite settings

This role ensures your BBB servers are properly integrated with the Scalelite load balancer for automatic meeting distribution and high availability.
