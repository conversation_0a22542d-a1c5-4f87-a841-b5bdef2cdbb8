---
# Tasks for unregistering a BBB server from Scalelite load balancer

# Skip if Scalelite is offline
- name: Skip unregistration if Scalelite offline
  debug:
    msg: "Scalelite server unreachable. Skipping unregistration."
  when: not scalelite_online

- name: End play if Scalelite offline
  meta: end_play
  when: not scalelite_online

# Check if server exists in Scalelite
- name: Check if server exists in Scalelite
  debug:
    msg:
      - "Server {{ server.server_name }} {{ 'found' if scalelite_server_id != '' else 'not found' }} in Scalelite"
      - "Scalelite Server ID: {{ scalelite_server_id | default('None') }}"
      - "Server URL: {{ server.url }}"
      - "Scalelite online: {{ scalelite_online }}"

# Disable server in Scalelite first (with retries)
- name: Disable server in Scalelite
  command: >-
    sshpass -p "{{ scalelite.ssh_password | default('Np66W3PnuK4j') }}" ssh -o StrictHostKeyChecking=no
    {{ scalelite.server_user | default('root') }}@{{ scalelite.server_ip | default('**************') }}
    "docker exec scalelite-api bundle exec rake \"servers:disable[{{ scalelite_server_id }}]\""
  register: scalelite_disable
  failed_when: false
  changed_when: scalelite_disable.rc == 0
  retries: 3
  delay: 5
  when:
    - scalelite_server_id is defined
    - scalelite_server_id != ''

# Remove server from Scalelite (with retries)
- name: Remove server from Scalelite
  command: >-
    sshpass -p "{{ scalelite.ssh_password | default('Np66W3PnuK4j') }}" ssh -o StrictHostKeyChecking=no
    {{ scalelite.server_user | default('root') }}@{{ scalelite.server_ip | default('**************') }}
    "docker exec scalelite-api bundle exec rake \"servers:remove[{{ scalelite_server_id }}]\""
  register: scalelite_remove
  failed_when: false  # Don't fail immediately, we'll check the result
  changed_when: scalelite_remove.rc == 0
  retries: 3
  delay: 5
  when:
    - scalelite_server_id is defined
    - scalelite_server_id != ''

# Log removal result
- name: Log Scalelite removal result
  debug:
    msg:
      - "Scalelite removal command result:"
      - "Return code: {{ scalelite_remove.rc | default('N/A') }}"
      - "Stdout: {{ scalelite_remove.stdout | default('N/A') }}"
      - "Stderr: {{ scalelite_remove.stderr | default('N/A') }}"
  when:
    - scalelite_server_id is defined
    - scalelite_server_id != ''
    - scalelite_remove is defined

# Fail if removal actually failed (not just server not found)
- name: Fail if server removal failed
  fail:
    msg: "Failed to remove server {{ scalelite_server_id }} from Scalelite: {{ scalelite_remove.stderr | default(scalelite_remove.stdout) }}"
  when:
    - scalelite_server_id is defined
    - scalelite_server_id != ''
    - scalelite_remove is defined
    - scalelite_remove.rc != 0
    - "'not found' not in (scalelite_remove.stderr | default('') | lower)"
    - "'does not exist' not in (scalelite_remove.stderr | default('') | lower)"

# Display unregistration status
- name: Display unregistration status
  debug:
    msg: >
      {% if scalelite_server_id is defined and scalelite_server_id != '' and scalelite_remove.rc == 0 %}
      ✅ Server {{ server.server_name }} successfully removed from Scalelite.
      {% elif scalelite_server_id is defined and scalelite_server_id != '' and scalelite_remove.rc != 0 %}
      {% if 'not found' in (scalelite_remove.stderr | default('') | lower) or 'does not exist' in (scalelite_remove.stderr | default('') | lower) %}
      ℹ️  Server {{ server.server_name }} was not found in Scalelite (may have already been removed).
      {% else %}
      ❌ Failed to remove server {{ server.server_name }} from Scalelite. Error: {{ scalelite_remove.stderr | default(scalelite_remove.stdout) }}
      {% endif %}
      {% else %}
      ℹ️  Server {{ server.server_name }} not found in Scalelite. May have already been removed.
      {% endif %}
