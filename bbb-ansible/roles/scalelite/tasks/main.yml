---
# Main tasks file for Scalelite role
# This file serves as the entry point for the role and determines which tasks to run

# Set default action and server variables
- name: Set Scalelite defaults and server variables
  set_fact:
    scalelite_action: "{{ scalelite_action | default('register') }}"
    server:
      id: "{{ server_id | default(inventory_hostname) }}"
      server_name: "{{ server_name | default(server_id | default(inventory_hostname)) }}"
      url: "https://{{ dns_name | default(server_name | default(server_id | default(inventory_hostname))) }}.{{ base_domain | default('geerd.net') }}/bigbluebutton/api"

# Get BBB secret if not already available
- name: Get BBB secret
  shell: bbb-conf --secret | grep -E '^Secret:' | awk '{print $2}'
  register: bbb_secret_output
  become: true
  when: bbb_server_secret is not defined

- name: Set BBB secret fact
  set_fact:
    bbb_server_secret: "{{ bbb_secret_output.stdout }}"
  when: bbb_server_secret is not defined

# Ensure sshpass is installed for password-based SSH
- name: Ensure sshpass is installed
  apt:
    name: sshpass
    state: present
  become: true
  when: ansible_os_family == "Debian"

# Check if Scalelite server is reachable
- name: Check Scalelite server connectivity
  wait_for:
    host: "{{ scalelite.server_ip | default('**************') }}"
    port: 22
    timeout: 5
  register: scalelite_reachable
  ignore_errors: yes

# Set connectivity status
- name: Set Scalelite connectivity status
  set_fact:
    scalelite_online: "{{ scalelite_reachable.failed is not defined or not scalelite_reachable.failed }}"

# Get server list from Scalelite for ID extraction
- name: Get server list from Scalelite
  command: >-
    sshpass -p "{{ scalelite.ssh_password | default('Np66W3PnuK4j') }}" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10
    {{ scalelite.server_user | default('root') }}@{{ scalelite.server_ip | default('**************') }}
    "docker exec scalelite-api bundle exec rake servers"
  register: scalelite_servers
  when: scalelite_online
  failed_when: false
  changed_when: false
  timeout: 30

# Debug server list output
- name: Debug server list output
  debug:
    msg:
      - "Looking for server URL: {{ server.url }}"
      - "Scalelite online: {{ scalelite_online }}"
      - "Scalelite servers output:"
      - "{{ scalelite_servers.stdout }}"
  when:
    - scalelite_online
    - scalelite_servers.stdout is defined

# Extract server ID using reliable method - find ID before our URL
- name: Extract server ID from Scalelite server list
  set_fact:
    scalelite_server_id: >-
      {%- set lines = scalelite_servers.stdout.split('\n') -%}
      {%- set found_id = '' -%}
      {%- set found = false -%}
      {%- for i in range(lines|length) if not found -%}
        {%- set line = lines[i].strip() -%}
        {%- if line.startswith('id: ') -%}
          {%- set current_id = line[4:].strip() -%}
          {%- for j in range(i+1, (i+10)|min(lines|length)) if not found -%}
            {%- if server.url in lines[j] -%}
              {%- set found_id = current_id -%}
              {%- set found = true -%}
            {%- endif -%}
          {%- endfor -%}
        {%- endif -%}
      {%- endfor -%}
      {{ found_id }}
  when:
    - scalelite_online
    - scalelite_servers.stdout is defined
    - server.url in scalelite_servers.stdout

# Fallback: Try shell-based extraction if Jinja2 method failed
- name: Fallback server ID extraction using shell
  shell: |
    echo "{{ scalelite_servers.stdout }}" | awk '
    /^id:/ { id = $2 }
    /{{ server.url | regex_escape }}/ { print id; exit }
    '
  register: scalelite_id_shell_extraction
  when:
    - scalelite_online
    - scalelite_servers.stdout is defined
    - server.url in scalelite_servers.stdout
    - (scalelite_server_id is not defined or scalelite_server_id == "")
  changed_when: false
  failed_when: false

- name: Set shell-extracted server ID
  set_fact:
    scalelite_server_id: "{{ scalelite_id_shell_extraction.stdout.strip() }}"
  when:
    - scalelite_online
    - scalelite_servers.stdout is defined
    - server.url in scalelite_servers.stdout
    - scalelite_id_shell_extraction.stdout is defined
    - scalelite_id_shell_extraction.stdout.strip() != ""
    - (scalelite_server_id is not defined or scalelite_server_id == "")

# Debug extracted server ID
- name: Debug extracted server ID
  debug:
    msg:
      - "Extracted Scalelite server ID: '{{ scalelite_server_id | default('NOT_FOUND') }}'"
      - "Server URL: {{ server.url }}"
      - "Shell extraction result: {{ scalelite_id_shell_extraction.stdout | default('N/A') }}"
  when:
    - scalelite_online
    - scalelite_servers.stdout is defined

- name: Set empty server ID if not found
  set_fact:
    scalelite_server_id: ""
  when:
    - scalelite_online
    - scalelite_servers.stdout is defined
    - (scalelite_server_id is not defined or scalelite_server_id == "" or server.url not in scalelite_servers.stdout)

# Include action-specific tasks
- name: Include register tasks
  include_tasks: register_server.yml
  when: scalelite_action == 'register'

- name: Include unregister tasks
  include_tasks: unregister_server.yml
  when: scalelite_action == 'unregister'
