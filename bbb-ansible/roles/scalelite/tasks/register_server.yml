---
# Tasks for registering a BBB server with Scalelite load balancer

# Verify B<PERSON> secret is available
- name: Check BBB secret availability
  assert:
    that: bbb_server_secret is defined
    fail_msg: "BBB secret is not available. Cannot register with Scalelite."

# Skip if Scalelite is offline
- name: Skip registration if Scalelite offline
  debug:
    msg: "Scalelite server unreachable. Skipping registration."
  when: not scalelite_online

- name: End play if Scalelite offline
  meta: end_play
  when: not scalelite_online

# Register server with Scalelite
- name: Register server with Scalelite
  command: >-
    sshpass -p "{{ scalelite.ssh_password | default('Np66W3PnuK4j') }}" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10
    {{ scalelite.server_user | default('root') }}@{{ scalelite.server_ip | default('**************') }}
    "docker exec scalelite-api bundle exec rake \"servers:add[{{ server.url }},{{ bbb_server_secret }}]\""
  register: scalelite_register
  failed_when: false
  changed_when: scalelite_register.rc == 0

- name: Debug Scalelite registration command and output
  debug:
    msg:
      - "Registration command executed for: {{ server.url }}"
      - "BBB Secret: {{ bbb_server_secret[:8] }}..."
      - "Return code: {{ scalelite_register.rc }}"
      - "Stdout: {{ scalelite_register.stdout }}"
      - "Stderr: {{ scalelite_register.stderr }}"

# Extract server ID from registration output if needed
- name: Extract server ID from registration output
  set_fact:
    scalelite_server_id: "{{ scalelite_register.stdout | regex_search('id: ([a-zA-Z0-9-]+)', '\\1') | first | default(scalelite_server_id | default('')) }}"
  when:
    - scalelite_register.stdout is defined
    - scalelite_register.stdout | regex_search('id: ([a-zA-Z0-9-]+)') is defined

# Enable server in Scalelite
- name: Enable server in Scalelite
  command: >-
    sshpass -p "{{ scalelite.ssh_password | default('Np66W3PnuK4j') }}" ssh -o StrictHostKeyChecking=no
    {{ scalelite.server_user | default('root') }}@{{ scalelite.server_ip | default('**************') }}
    "docker exec scalelite-api bundle exec rake \"servers:enable[{{ scalelite_server_id }}]\""
  register: scalelite_enable
  failed_when: false
  changed_when: scalelite_enable.rc == 0
  when:
    - scalelite_server_id is defined
    - scalelite_server_id != ''

# Display registration status
- name: Display registration status
  debug:
    msg: >
      {% if scalelite_register.rc == 0 and scalelite_enable.rc == 0 %}
      ✅ Server {{ server.server_name }} registered and enabled with Scalelite successfully.
      {% elif scalelite_register.rc == 0 %}
      ✅ Server {{ server.server_name }} registered with Scalelite successfully.
      {% elif 'already exists' in (scalelite_register.stdout | default('')) and scalelite_enable.rc == 0 %}
      ℹ️  Server {{ server.server_name }} was already registered and has been enabled in Scalelite.
      {% elif 'already exists' in (scalelite_register.stdout | default('')) %}
      ℹ️  Server {{ server.server_name }} was already registered with Scalelite.
      {% else %}
      ❌ Failed to register server with Scalelite. Check the configuration.
      {% endif %}
