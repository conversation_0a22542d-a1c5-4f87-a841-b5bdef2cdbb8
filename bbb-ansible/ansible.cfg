[defaults]
inventory = inventory/droplet.py
host_key_checking = False
retry_files_enabled = False
roles_path = roles
timeout = 60
deprecation_warnings = False
interpreter_python = auto_silent
allow_world_readable_tmpfiles = True
gathering = smart
fact_caching = memory
callback_whitelist = timer, profile_tasks

[ssh_connection]
pipelining = True
ssh_args = -o ControlMaster=auto -o ControlPersist=60s -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null
retries = 3

[persistent_connection]
command_timeout = 60
connect_timeout = 30
