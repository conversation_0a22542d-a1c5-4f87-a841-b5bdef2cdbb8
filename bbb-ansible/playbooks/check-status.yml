---
# Playbook to check processing server status
- name: Check processing server status
  hosts: processing_servers
  gather_facts: yes
  become: yes

  tasks:
    - name: Check disk usage for recordings
      shell: df -h /var/bigbluebutton
      register: disk_usage
      failed_when: false

    - name: Check raw processor service status
      systemd:
        name: raw-processor
      register: service_status
      failed_when: false

    - name: Count pending recordings
      shell: find /var/bigbluebutton/recording/raw -name "*.done" | wc -l
      register: pending_recordings
      failed_when: false

    - name: Check processing directory
      shell: ls -la /var/bigbluebutton/processing/ | wc -l
      register: processing_files
      failed_when: false

    - name: Display comprehensive status
      debug:
        msg:
          - "🖥️  Server: {{ inventory_hostname }}"
          - "💾 Disk usage: {{ disk_usage.stdout | default('N/A') }}"
          - "🔄 Raw processor service: {{ service_status.status.ActiveState | default('unknown') }}"
          - "📼 Pending recordings: {{ pending_recordings.stdout | default('0') }}"
          - "📁 Processing files: {{ (processing_files.stdout | int - 3) | default('0') }}"
          - "⏰ Check time: {{ ansible_date_time.iso8601 }}"
