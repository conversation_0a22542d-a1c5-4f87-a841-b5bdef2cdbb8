---
# Main playbook for provisioning BigBlueButton servers
- name: Provision BigBlueButton Servers
  hosts: all
  become: yes
  gather_facts: yes

  pre_tasks:
    - name: Set server variables from extra vars
      set_fact:
        server:
          id: "{{ server_id | default(inventory_hostname) }}"
          type: "{{ server_type | default('medium') }}"
          capacity: "{{ server_capacity | default(server_types[server_type | default('medium')].capacity) }}"
          full_id: "{{ server_id | default(inventory_hostname) }}"
          server_name: "{{ server_name | default(server_id | default(inventory_hostname)) }}"

    - name: Display server information
      debug:
        msg:
          - "Server ID: {{ server.id }}"
          - "Server Full ID: {{ server.full_id }}"
          - "Server Name: {{ server.server_name }}"
          - "Server IP: {{ ansible_host | default('N/A') }}"
          - "Server Type: {{ server.type }}"
          - "Server Capacity: {{ server.capacity }}"
          - "Server Domain: {{ dns_name | default(server.server_name + '.' + base_domain) }}"
          - "DNS Status: {{ 'DNS record created' if dns_name is defined else 'Using fallback domain construction' }}"
          - "Base Domain: {{ base_domain | default('geerd.net') }}"
          - "Email: {{ email | default('<EMAIL>') }}"
          - "Scalelite Integration: {{ 'Enabled' if (scalelite | default({})).enabled | default(false) else 'Disabled' }}"

  roles:
    - role: bbb
      tags: [bbb]

    - role: scalelite
      tags: [scalelite]
      vars:
        scalelite_action: "register"

    - role: bbb_recording
      tags: [recording]
      when:
        - bbb.recording_enabled | default(true)
        - server_role is not defined or server_role == "recording"
      # Sets up RAW file transfer only - no local processing

    # Note: Processing server is configured manually (standalone)
