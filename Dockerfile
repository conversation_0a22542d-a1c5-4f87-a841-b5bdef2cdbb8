# ================================================================================================
# BBB Manager Production Dockerfile
# ================================================================================================
# Simplified production-ready container for BBB Manager with NestJS and Ansible

# ================================================================================================
# Stage 1: Build Stage - Compile TypeScript
# ================================================================================================
FROM node:18-alpine AS builder

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++ git

# Copy package files and install ALL dependencies (including dev dependencies for build)
COPY nest-app/package*.json ./nest-app/
WORKDIR /app/nest-app
RUN npm ci && npm cache clean --force

# Copy source code and build
COPY nest-app/ ./
RUN npm run build

# ================================================================================================
# Stage 2: Production Stage - Runtime environment
# ================================================================================================
FROM node:18-alpine AS production

# Install system dependencies and Python packages in one layer to reduce size
RUN apk add --no-cache \
    python3 \
    py3-pip \
    py3-virtualenv \
    ansible \
    openssh-client \
    sshpass \
    curl \
    bash \
    git \
    ca-certificates \
    && python3 -m venv /opt/venv \
    && /opt/venv/bin/pip install --no-cache-dir \
        requests \
        python-digitalocean \
    && rm -rf /var/cache/apk/* \
    && rm -rf /root/.cache/pip

# Set virtual environment in PATH
ENV PATH="/opt/venv/bin:$PATH"

# Install Ansible collections
RUN ansible-galaxy collection install \
    community.general \
    community.digitalocean \
    ansible.posix

# Create non-root user
RUN addgroup -g 1001 -S bbb-manager && \
    adduser -u 1001 -S bbb-manager -G bbb-manager -h /home/<USER>/bin/bash

# Set working directory
WORKDIR /app

# Copy built application and package files from builder stage
COPY --from=builder --chown=bbb-manager:bbb-manager /app/nest-app/dist ./nest-app/dist
COPY --from=builder --chown=bbb-manager:bbb-manager /app/nest-app/package*.json ./nest-app/

# Install only production dependencies in final stage
WORKDIR /app/nest-app
RUN npm ci --only=production && npm cache clean --force
WORKDIR /app

# Copy Ansible configuration
COPY --chown=bbb-manager:bbb-manager bbb-ansible/ ./bbb-ansible/

# Create directories
RUN mkdir -p /app/logs /app/tmp /home/<USER>/.ssh /home/<USER>/.ansible && \
    chown -R bbb-manager:bbb-manager /app /home/<USER>/.ssh /home/<USER>/.ansible && \
    chmod 700 /home/<USER>/.ssh

# Environment variables
ENV NODE_ENV=production \
    PORT=3000 \
    TZ=UTC \
    PATH="/opt/venv/bin:$PATH" \
    ANSIBLE_HOST_KEY_CHECKING=False \
    ANSIBLE_SSH_RETRIES=3 \
    ANSIBLE_TIMEOUT=30 \
    ANSIBLE_GATHERING=smart \
    ANSIBLE_STDOUT_CALLBACK=yaml \
    ANSIBLE_ROLES_PATH=/app/bbb-ansible/roles \
    ANSIBLE_INVENTORY=/app/bbb-ansible/inventory \
    ANSIBLE_CONFIG=/app/bbb-ansible/ansible.cfg

# Switch to non-root user
USER bbb-manager

# Set working directory
WORKDIR /app/nest-app

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/bbb-scheduler/status || exit 1

# Start application
CMD ["node", "dist/main.js"]
