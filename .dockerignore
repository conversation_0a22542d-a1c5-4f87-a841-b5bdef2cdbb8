# ================================================================================================
# Docker Ignore File for BBB Manager
# Optimizes Docker build by excluding unnecessary files and directories
# ================================================================================================

# ================================================================================================
# Node.js and NPM
# ================================================================================================
node_modules/
nest-app/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn
package-lock.json
yarn.lock

# ================================================================================================
# Build Outputs and Compiled Files
# ================================================================================================
nest-app/dist/
build/
out/
*.tsbuildinfo

# ================================================================================================
# Environment and Configuration Files
# ================================================================================================
.env
.env.*
!.env.example
nest-app/.env
nest-app/.env.*
!nest-app/.env.example

# ================================================================================================
# Development and IDE Files
# ================================================================================================
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.project
.classpath
.c9/
*.launch
.settings/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# ================================================================================================
# Testing and Coverage
# ================================================================================================
coverage/
.nyc_output/
test-results/
junit.xml

# ================================================================================================
# Logs and Temporary Files
# ================================================================================================
logs/
*.log
.tmp/
*.tmp
*.temp
/tmp/

# ================================================================================================
# OS Generated Files
# ================================================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*~

# ================================================================================================
# Git and Version Control
# ================================================================================================
.git/
.gitignore
.gitattributes
.gitmodules

# ================================================================================================
# Documentation and README files (keep essential ones)
# ================================================================================================
*.md
!README.md
docs/
documentation/

# ================================================================================================
# Ansible Specific Exclusions
# ================================================================================================
bbb-ansible/**/*.retry
bbb-ansible/inventory/hosts.yml
bbb-ansible/.vault_pass

# ================================================================================================
# Docker Related Files
# ================================================================================================
Dockerfile*
.dockerignore

# ================================================================================================
# CI/CD and Deployment
# ================================================================================================
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
azure-pipelines.yml
Jenkinsfile

# ================================================================================================
# Security and Secrets
# ================================================================================================
*.pem
*.key
*.crt
*.p12
*.pfx
secrets/
.secrets/

# ================================================================================================
# Backup and Archive Files
# ================================================================================================
*.bak
*.backup
*.old
*.orig
*.tar
*.tar.gz
*.zip
*.rar

# ================================================================================================
# Editor and IDE Specific
# ================================================================================================
.editorconfig
.eslintrc*
.prettierrc*
.stylelintrc*
tsconfig*.json
!tsconfig.json
jest.config.*
