import { Controller, Get, Param } from '@nestjs/common';
import { LectureCacheService } from './lecture-cache.service';
import { RedisService } from './redis.service';

@Controller('cache-debug')
export class CacheDebugController {
  constructor(
    private readonly lectureCacheService: LectureCacheService,
    private readonly redisService: RedisService,
  ) {}

  @Get('lectures')
  async getAllLectures() {
    return await this.lectureCacheService.getAllProcessedLectures();
  }

  @Get('stats')
  async getCacheStats() {
    return await this.lectureCacheService.getCacheStats();
  }

  @Get('keys')
  async getAllKeys() {
    return await this.redisService.keys('*');
  }

  @Get('info')
  async getRedisInfo() {
    const client = this.redisService.getClient();
    const info = await client.info();
    const dbsize = await client.dbsize();

    return {
      dbsize,
      memory: info.split('\n').find(line => line.includes('used_memory_human'))?.split(':')[1]?.trim(),
      uptime: info.split('\n').find(line => line.includes('uptime_in_seconds'))?.split(':')[1]?.trim(),
      connected_clients: info.split('\n').find(line => line.includes('connected_clients'))?.split(':')[1]?.trim(),
      keyspace_hits: info.split('\n').find(line => line.includes('keyspace_hits'))?.split(':')[1]?.trim(),
      keyspace_misses: info.split('\n').find(line => line.includes('keyspace_misses'))?.split(':')[1]?.trim(),
    };
  }

  @Get('servers')
  async getAllServers() {
    return await this.lectureCacheService.getAllServers();
  }

  @Get('servers/stats')
  async getServerStats() {
    return await this.lectureCacheService.getServerStats();
  }

  @Get('servers/:status')
  async getServersByStatus(@Param('status') status: string) {
    return await this.lectureCacheService.getServersByStatus(status as any);
  }

  @Get('monitor')
  async getRecentOperations() {
    const client = this.redisService.getClient();
    const [
      lectureStats,
      serverStats,
      redisInfo,
      dbsize
    ] = await Promise.all([
      this.lectureCacheService.getCacheStats(),
      this.lectureCacheService.getServerStats(),
      client.info(),
      client.dbsize()
    ]);

    // Parse memory usage
    const memoryInfo = redisInfo.split('\n').find(line => line.includes('used_memory_human'))?.split(':')[1]?.trim();
    const connectionsInfo = redisInfo.split('\n').find(line => line.includes('connected_clients'))?.split(':')[1]?.trim();

    return {
      timestamp: new Date().toISOString(),
      cache: {
        lectures: lectureStats,
        servers: serverStats,
      },
      redis: {
        totalKeys: dbsize,
        memoryUsage: memoryInfo,
        connections: connectionsInfo,
      },
      health: {
        status: dbsize > 0 ? 'active' : 'idle',
        cacheUtilization: `${lectureStats.total + serverStats.total} total items`
      }
    };
  }
}