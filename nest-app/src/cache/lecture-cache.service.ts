import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';
import {
  ServerStatus,
  LectureStatus,
  LectureProcessingInfo,
  ServerCacheInfo
} from '../interfaces/bbb-scheduler.interface';

@Injectable()
export class LectureCacheService {
  private readonly logger = new Logger(LectureCacheService.name);
  private readonly LECTURE_PREFIX = 'lecture:';
  private readonly SERVER_PREFIX = 'server:';
  private readonly LECTURE_SET = 'processed_lectures';
  private readonly SERVER_SET = 'active_servers';
  private readonly DEFAULT_TTL = 24 * 60 * 60; // 24 hours in seconds

  constructor(private readonly redisService: RedisService) {}

  /**
   * Check if a lecture has already been processed
   */
  async isLectureProcessed(lectureId: string): Promise<boolean> {
    const exists = await this.redisService.exists(this.getLectureKey(lectureId));
    if (exists) {
      this.logger.log(`Lecture ${lectureId} already processed`);
    }
    return exists;
  }

  /**
   * Get lecture processing information
   */
  async getLectureInfo(lectureId: string): Promise<LectureProcessingInfo | null> {
    return await this.redisService.get<LectureProcessingInfo>(this.getLectureKey(lectureId));
  }

  /**
   * Mark a lecture as scheduled/processed
   */
  async markLectureAsScheduled(lectureId: string, startTime: string, endTime: string, participants: number): Promise<void> {
    const info: LectureProcessingInfo = {
      lectureId,
      processedAt: new Date().toISOString(),
      startTime,
      endTime,
      status: LectureStatus.PROCESSED,
      participants,
    };

    await this.redisService.set(this.getLectureKey(lectureId), info, this.DEFAULT_TTL);
    this.logger.log(`Marked lecture ${lectureId} as scheduled for ${startTime}`);
  }

  /**
   * Update lecture with server information
   */
  async updateLectureWithServer(
    lectureId: string,
    serverId: string,
    serverName: string,
    ipAddress: string,
    dnsName: string
  ): Promise<void> {
    const info = await this.getLectureInfo(lectureId);
    if (info) {
      info.serverId = serverId;
      await this.redisService.set(this.getLectureKey(lectureId), info, this.DEFAULT_TTL);
      this.logger.log(`Updated lecture ${lectureId} with server info: ${serverName} (${ipAddress})`);
    } else {
      this.logger.warn(`Lecture ${lectureId} not found when updating with server info`);
    }
  }

  /**
   * Get all processed lectures
   */
  async getAllProcessedLectures(): Promise<LectureProcessingInfo[]> {
    try {
      const keys = await this.redisService.keys(`${this.LECTURE_PREFIX}*`);
      const lectures: LectureProcessingInfo[] = [];

      for (const key of keys) {
        try {
          const info = await this.redisService.get<LectureProcessingInfo>(key);
          if (info) {
            lectures.push(info);
          }
        } catch (error) {
          this.logger.error(`Failed to parse lecture data for key ${key}: ${error.message}`);
        }
      }

      return lectures.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());
    } catch (error) {
      this.logger.error(`Failed to get all processed lectures: ${error.message}`);
      return [];
    }
  }

  /**
   * Get lectures by status
   */
  async getLecturesByStatus(status: LectureProcessingInfo['status']): Promise<LectureProcessingInfo[]> {
    const allLectures = await this.getAllProcessedLectures();
    return allLectures.filter(lecture => lecture.status === status);
  }

  /**
   * Remove lecture from cache (for cleanup)
   */
  async removeLecture(lectureId: string): Promise<void> {
    await this.redisService.del(this.getLectureKey(lectureId));
    this.logger.log(`Removed lecture ${lectureId} from cache`);
  }

  /**
   * Clean up old lectures (older than specified hours)
   */
  async cleanupOldLectures(hoursOld: number = 48): Promise<number> {
    try {
      const cutoffTime = new Date(Date.now() - hoursOld * 60 * 60 * 1000);
      const allLectures = await this.getAllProcessedLectures();
      let removedCount = 0;

      for (const lecture of allLectures) {
        try {
          const lectureEndTime = new Date(lecture.endTime);
          if (lectureEndTime < cutoffTime && lecture.status === LectureStatus.PROCESSED) {
            await this.removeLecture(lecture.lectureId);
            removedCount++;
          }
        } catch (error) {
          this.logger.error(`Failed to cleanup lecture ${lecture.lectureId}: ${error.message}`);
        }
      }

      if (removedCount > 0) {
        this.logger.log(`Cleaned up ${removedCount} old lectures`);
      }

      return removedCount;
    } catch (error) {
      this.logger.error(`Failed to cleanup old lectures: ${error.message}`);
      return 0;
    }
  }

  /**
   * Get lecture cache statistics
   */
  async getCacheStats(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    oldest: string | null;
    newest: string | null;
  }> {
    const allLectures = await this.getAllProcessedLectures();
    const byStatus: Record<string, number> = {};

    allLectures.forEach(lecture => {
      byStatus[lecture.status] = (byStatus[lecture.status] || 0) + 1;
    });

    return {
      total: allLectures.length,
      byStatus,
      oldest: allLectures.length > 0 ? allLectures[0].startTime : null,
      newest: allLectures.length > 0 ? allLectures[allLectures.length - 1].startTime : null,
    };
  }

  // =====================================================
  // SERVER CACHE MANAGEMENT METHODS
  // =====================================================

  /**
   * Cache a new server when it starts being created
   */
  async cacheServerAsCreating(
    serverId: string,
    serverName: string,
    size: string,
    lectureIds: string[],
    totalParticipants: number,
    startTime: string,
    endTime: string
  ): Promise<void> {
    const serverInfo: ServerCacheInfo = {
      serverId,
      serverName,
      size,
      status: ServerStatus.PROVISIONING,
      createdAt: new Date().toISOString(),
      lectures: lectureIds,
      totalParticipants,
      startTime,
      endTime,
    };

    await this.redisService.set(this.getServerKey(serverId), serverInfo, this.DEFAULT_TTL);
    await this.redisService.getClient().sadd(this.SERVER_SET, serverId);

    this.logger.log(`Server ${serverId} cached as provisioning with ${lectureIds.length} lectures`);
  }

  /**
   * Update server status to ready when it's fully provisioned
   */
  async markServerAsReady(serverId: string, ipAddress: string, dnsName?: string): Promise<void> {
    const serverInfo = await this.getServerInfo(serverId);
    if (serverInfo) {
      serverInfo.status = ServerStatus.READY;
      serverInfo.readyAt = new Date().toISOString();
      serverInfo.ipAddress = ipAddress;
      if (dnsName) {
        serverInfo.dnsName = dnsName;
      }

      await this.redisService.set(this.getServerKey(serverId), serverInfo, this.DEFAULT_TTL);
      this.logger.log(`Server ${serverId} marked as ready at ${ipAddress}`);
    } else {
      this.logger.warn(`Server ${serverId} not found when marking as ready`);
    }
  }

  /**
   * Remove server from cache when it's destroyed
   */
  async removeServer(serverId: string): Promise<void> {
    const exists = await this.isServerCached(serverId);
    if (exists) {
      await this.redisService.del(this.getServerKey(serverId));
      await this.redisService.getClient().srem(this.SERVER_SET, serverId);
      this.logger.log(`Server ${serverId} removed from cache`);
    } else {
      this.logger.warn(`Server ${serverId} not found when attempting to remove`);
    }
  }

  /**
   * Get server information
   */
  async getServerInfo(serverId: string): Promise<ServerCacheInfo | null> {
    return await this.redisService.get<ServerCacheInfo>(this.getServerKey(serverId));
  }

  /**
   * Get all cached servers
   */
  async getAllServers(): Promise<ServerCacheInfo[]> {
    try {
      const serverIds = await this.redisService.getClient().smembers(this.SERVER_SET);
      const servers: ServerCacheInfo[] = [];

      for (const serverId of serverIds) {
        try {
          const serverInfo = await this.getServerInfo(serverId);
          if (serverInfo) {
            servers.push(serverInfo);
          } else {
            // Clean up orphaned server ID from set
            await this.redisService.getClient().srem(this.SERVER_SET, serverId);
            this.logger.warn(`Removed orphaned server ID ${serverId} from server set`);
          }
        } catch (error) {
          this.logger.error(`Failed to get server info for ${serverId}: ${error.message}`);
        }
      }

      return servers.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
    } catch (error) {
      this.logger.error(`Failed to get all servers: ${error.message}`);
      return [];
    }
  }

  /**
   * Get servers by status
   */
  async getServersByStatus(status: ServerCacheInfo['status']): Promise<ServerCacheInfo[]> {
    const allServers = await this.getAllServers();
    return allServers.filter(server => server.status === status);
  }

  /**
   * Check if a server exists in cache
   */
  async isServerCached(serverId: string): Promise<boolean> {
    return await this.redisService.exists(this.getServerKey(serverId));
  }

  /**
   * Get server statistics
   */
  async getServerStats(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    bySize: Record<string, number>;
  }> {
    const allServers = await this.getAllServers();
    const byStatus: Record<string, number> = {};
    const bySize: Record<string, number> = {};

    allServers.forEach(server => {
      byStatus[server.status] = (byStatus[server.status] || 0) + 1;
      bySize[server.size] = (bySize[server.size] || 0) + 1;
    });

    return {
      total: allServers.length,
      byStatus,
      bySize,
    };
  }

  /**
   * Update server status to any valid ServerStatus
   */
  async updateServerStatus(serverId: string, status: ServerCacheInfo['status']): Promise<void> {
    const serverInfo = await this.getServerInfo(serverId);
    if (serverInfo) {
      serverInfo.status = status;

      // Add timestamp for specific status transitions
      if (status === ServerStatus.READY && !serverInfo.readyAt) {
        serverInfo.readyAt = new Date().toISOString();
      }

      await this.redisService.set(this.getServerKey(serverId), serverInfo, this.DEFAULT_TTL);
      this.logger.log(`Server ${serverId} status updated to ${status}`);
    } else {
      this.logger.warn(`Server ${serverId} not found when updating status to ${status}`);
    }
  }

  /**
   * Utility Methods
   */
  private getLectureKey(lectureId: string): string {
    return `${this.LECTURE_PREFIX}${lectureId}`;
  }

  private getServerKey(serverId: string): string {
    return `${this.SERVER_PREFIX}${serverId}`;
  }

  /**
   * Validate if server info exists before operations
   */
  private async validateServerExists(serverId: string, operation: string): Promise<ServerCacheInfo | null> {
    const serverInfo = await this.getServerInfo(serverId);
    if (!serverInfo) {
      this.logger.warn(`Server ${serverId} not found for operation: ${operation}`);
    }
    return serverInfo;
  }

  /**
   * Validate if lecture info exists before operations
   */
  private async validateLectureExists(lectureId: string, operation: string): Promise<LectureProcessingInfo | null> {
    const lectureInfo = await this.getLectureInfo(lectureId);
    if (!lectureInfo) {
      this.logger.warn(`Lecture ${lectureId} not found for operation: ${operation}`);
    }
    return lectureInfo;
  }
}
