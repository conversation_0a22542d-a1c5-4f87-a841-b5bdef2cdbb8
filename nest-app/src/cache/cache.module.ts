import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisService } from './redis.service';
import { LectureCacheService } from './lecture-cache.service';
import { CacheDebugController } from './cache-debug.controller';

@Module({
  imports: [ConfigModule],
  controllers: [CacheDebugController],
  providers: [RedisService, LectureCacheService],
  exports: [RedisService, LectureCacheService],
})
export class CacheModule {}
