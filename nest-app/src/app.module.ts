import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { HttpModule } from '@nestjs/axios';
import { BbbSchedulerModule } from './bbb/bbb-scheduler.module';
import { DnsModule } from './dns';
import { CacheModule } from './cache';

@Module({
  imports: [
    ConfigModule.forRoot(),
    ScheduleModule.forRoot(),
    HttpModule,
    DnsModule,
    BbbSchedulerModule,
    CacheModule,
  ],
})
export class AppModule {}