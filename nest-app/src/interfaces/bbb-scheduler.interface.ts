// TypeScript interfaces for BBB Scheduler

export interface Lecture {
  id: string;
  start_time: string;
  end_time: string;
  num_participants: number;
}

export enum EventType {
  START = 'start',
  END = 'end',
}

export interface Event {
  time: Date;
  type: EventType;
  lectureId: string;
  participants: number;
}

export interface TimeInterval {
  start: Date;
  end: Date;
  concurrentParticipants: number;
  lectures: string[];
}

export enum ServerSize {
  MEDIUM = 'medium',
  LARGE = 'large',
  EXTRA_LARGE = 'extra-large',
}

export enum ServerStatus {
  CREATING = 'creating',           // Droplet is being created
  PROVISIONING = 'provisioning',   // Ansible is provisioning the server
  READY = 'ready',                 // Server is ready for lectures
  DECOMMISSIONING = 'decommissioning', // Ansible is decommissioning the server
  DESTROYED = 'destroyed'          // Server has been destroyed
}

export enum LectureStatus {
  PROCESSED = 'processed',         // Lecture has been processed (server assigned)
  NOT_PROCESSED = 'not_processed'  // Lecture needs processing
}

// Simplified lecture tracking - just track if processed or not
export interface LectureProcessingInfo {
  lectureId: string;
  processedAt: string;
  startTime: string;
  endTime: string;
  status: LectureStatus;
  participants: number;
  serverId?: string;               // Which server this lecture is assigned to
}

export interface ServerConfig {
  size: ServerSize;
  vCPUs: number;
  ram: number;
  capacity: number;
  effectiveCapacity: number; // 80% of capacity
  slug: string; // DigitalOcean droplet size slug
}

export interface ServerAllocation {
  size: ServerSize;
  lectures: string[];
  totalParticipants: number;
  startTime: Date;
  endTime: Date;
  serverId?: string;
  ipAddress?: string;
  serverName?: string;
  dnsName?: string;
  // Cache/Status tracking fields
  status?: ServerStatus;
  createdAt?: string;
  readyAt?: string;
}

// Server cache specific interface with string dates for Redis storage
export interface ServerCacheInfo {
  serverId: string;
  serverName: string;
  size: string;
  lectures: string[];
  totalParticipants: number;
  startTime: string;  // ISO string for cache storage
  endTime: string;    // ISO string for cache storage
  ipAddress?: string;
  dnsName?: string;
  status: ServerStatus;
  createdAt: string;
  readyAt?: string;
}

export interface ServerOperation {
  operation: 'spin-up' | 'spin-down';
  serverSize: ServerSize;
  scheduledTime: Date;
  serverId?: string;
  lectures?: string[];
}
