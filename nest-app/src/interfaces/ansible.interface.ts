export interface AnsiblePlaybookOptions {
  inventory: string;
  playbook: string;
  variables?: Record<string, any>;
  options?: string[];
  environment?: Record<string, string>;
}

export interface AnsibleExecutionResult {
  success: boolean;
  error?: string;
  output: string[];
  duration: number;
  exitCode?: number;
}

export interface AnsibleServerProvisioningParams {
  serverId: string;
  serverType: string;
  ipAddress: string;
  dnsName: string;
}

export interface AnsibleServerDecommissioningParams {
  serverId: string;
  ipAddress: string;
  forceDecommission?: boolean;
}

export interface AnsibleStorageConfig {
  type: 'spaces';
  bucket_name: string;
  region: string;
  endpoint: string;
  access_key: string;
  secret_key: string;
}
