import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { BbbSchedulerService } from './bbb/bbb-scheduler.service';
import { Logger } from '@nestjs/common';

/**
 * Test script to verify GraphQL API fetching
 */
async function testApiFetch() {
  const logger = new Logger('TestApiFetch');
  logger.log('Starting API fetch test...');
  
  try {
    // Create NestJS application
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'], // Enable all log levels for detailed output
    });
    
    // Get the BBB Scheduler service
    const schedulerService = app.get(BbbSchedulerService);
    
    // Force production mode for testing
    process.env.NODE_ENV = 'production';
    
    logger.log('Triggering scheduler to fetch meetings...');
    
    // Trigger the scheduler
    const result = await schedulerService.scheduleDailyServers();
    
    logger.log('Scheduler completed successfully');
    logger.log(`Found ${result.serverAllocations.length} server allocations`);
    logger.log(`Scheduled ${result.scheduledOperations.length} operations`);
    
    // Log the first few allocations for verification
    if (result.serverAllocations.length > 0) {
      logger.log('Sample server allocations:');
      result.serverAllocations.slice(0, 3).forEach((allocation, index) => {
        logger.log(`Allocation ${index + 1}:`);
        logger.log(`  Size: ${allocation.size}`);
        logger.log(`  Participants: ${allocation.totalParticipants}`);
        logger.log(`  Start: ${allocation.startTime}`);
        logger.log(`  End: ${allocation.endTime}`);
        logger.log(`  Lectures: ${allocation.lectures.join(', ')}`);
      });
    }
    
    await app.close();
    logger.log('Test completed successfully');
  } catch (error) {
    logger.error(`Test failed: ${error.message}`, error.stack);
    process.exit(1);
  }
}

// Run the test
testApiFetch();
