import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as path from 'path';
import { spawn } from 'child_process';
import { AnsiblePlaybookOptions, AnsibleExecutionResult } from '../interfaces/ansible.interface';

@Injectable()
export class AnsibleService {
  private readonly logger = new Logger(AnsibleService.name);
  private readonly ansiblePath: string;
  private readonly inventoryPath: string;

  constructor(private readonly configService: ConfigService) {
    // Get the path to the bbb-ansible directory relative to the nest-app
    this.ansiblePath = path.resolve(__dirname, '../../../bbb-ansible');
    this.inventoryPath = path.join(this.ansiblePath, 'inventory/digitalocean.py');

    this.logger.log(`Ansible path: ${this.ansiblePath}`);
    this.logger.log(`Inventory path: ${this.inventoryPath}`);

    // Verify the ansible directory exists
    const fs = require('fs');
    if (!fs.existsSync(this.ansiblePath)) {
      this.logger.error(`Ansible path does not exist: ${this.ansiblePath}`);
    } else {
      this.logger.log(`Ansible path exists: ${this.ansiblePath}`);
      // Check if roles directory exists
      const rolesPath = path.join(this.ansiblePath, 'roles');
      if (fs.existsSync(rolesPath)) {
        this.logger.log(`Roles path exists: ${rolesPath}`);
        // List roles to verify
        const roles = fs.readdirSync(rolesPath);
        this.logger.log(`Available roles: ${roles.join(', ')}`);
      } else {
        this.logger.error(`Roles path does not exist: ${rolesPath}`);
      }
    }
  }

  /**
   * Provision a BigBlueButton server using Ansible with comprehensive verification
   * @param serverId The server ID (droplet ID)
   * @param serverType The server type (medium, large, extra-large)
   * @param ipAddress The server IP address
   * @param dnsName The DNS name for the server
   * @param serverName The server name for domain construction
   * @returns Promise<AnsibleExecutionResult>
   */
  async provisionServer(
    serverId: string,
    serverType: string,
    ipAddress: string,
    dnsName: string,
    serverName?: string
  ): Promise<AnsibleExecutionResult> {
    this.logger.log(`🚀 Starting server provisioning for ${serverId} (${serverType}) at ${ipAddress}`);

    try {
      // Step 1: Pre-flight checks
      const preflightResult = await this.runPreflightChecks();
      if (!preflightResult.success) {
        return preflightResult;
      }

      // Step 2: Wait for server to be SSH ready
      this.logger.log(`⏳ Waiting for SSH connectivity on ${ipAddress}...`);
      const sshReady = await this.waitForSSH(ipAddress);
      if (!sshReady) {
        return {
          success: false,
          error: `Server ${ipAddress} is not SSH accessible after timeout`,
          output: [],
          duration: 0
        };
      }

      // Step 3: Optionally wait for DNS propagation (non-blocking)
      if (dnsName) {
        this.logger.log(`🌐 Checking DNS propagation: ${dnsName}`);
        this.waitForDNS(dnsName, ipAddress).then(dnsReady => {
          if (dnsReady) {
            this.logger.log(`✅ DNS propagated successfully for ${dnsName}`);
          } else {
            this.logger.warn(`⚠️ DNS not fully propagated for ${dnsName}, but continuing`);
          }
        }).catch(() => {
          this.logger.warn(`⚠️ DNS check failed for ${dnsName}, but continuing`);
        });
      }

      // Step 4: Run Ansible provisioning
      this.logger.log(`⚙️ Running Ansible provisioning playbook...`);
      const provisionResult = await this.runProvisioningPlaybook(serverId, serverType, ipAddress, dnsName, serverName);

      if (!provisionResult.success) {
        return provisionResult;
      }

      // Step 5: Verify BBB services
      this.logger.log(`🔍 Verifying BBB services...`);
      const verificationResult = await this.verifyBBBServices(ipAddress);
      if (!verificationResult) {
        this.logger.warn(`⚠️ BBB services verification failed, but continuing`);
      }

      this.logger.log(`✅ Server ${serverId} provisioned successfully!`);
      return provisionResult;

    } catch (error) {
      this.logger.error(`❌ Error during server provisioning: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        output: [],
        duration: 0
      };
    }
  }

  /**
   * Run pre-flight checks before provisioning
   */
  private async runPreflightChecks(): Promise<AnsibleExecutionResult> {
    const fs = require('fs');
    const playbookPath = path.join(this.ansiblePath, 'playbooks/bbb-provision.yml');
    const rolesPath = path.join(this.ansiblePath, 'roles');

    // Check playbook exists
    if (!fs.existsSync(playbookPath)) {
      return {
        success: false,
        error: `Playbook not found: ${playbookPath}`,
        output: [],
        duration: 0
      };
    }

    // Check roles directory
    if (!fs.existsSync(rolesPath)) {
      return {
        success: false,
        error: `Roles directory not found: ${rolesPath}`,
        output: [],
        duration: 0
      };
    }

    const roles = fs.readdirSync(rolesPath);
    this.logger.log(`📋 Available roles: ${roles.join(', ')}`);

    return { success: true, error: undefined, output: [], duration: 0 };
  }

  /**
   * Wait for SSH connectivity to server
   */
  private async waitForSSH(ipAddress: string, maxAttempts: number = 30): Promise<boolean> {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const result = await this.testSSHConnection(ipAddress);
        if (result) {
          this.logger.log(`🔓 SSH connection established to ${ipAddress} (attempt ${attempt})`);
          return true;
        }
      } catch (error) {
        // Continue trying
      }

      this.logger.log(`⏳ SSH attempt ${attempt}/${maxAttempts} failed, retrying in 10s...`);
      await this.sleep(10000);
    }

    this.logger.error(`❌ SSH connection failed after ${maxAttempts} attempts`);
    return false;
  }

  /**
   * Test SSH connection to server
   */
  private async testSSHConnection(ipAddress: string): Promise<boolean> {
    return new Promise((resolve) => {
      const sshProcess = spawn('ssh', [
        '-o', 'ConnectTimeout=5',
        '-o', 'StrictHostKeyChecking=no',
        '-o', 'UserKnownHostsFile=/dev/null',
        '-o', 'BatchMode=yes',
        `root@${ipAddress}`,
        'echo "SSH_OK"'
      ]);

      let output = '';
      sshProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      sshProcess.on('close', (code) => {
        resolve(code === 0 && output.includes('SSH_OK'));
      });

      sshProcess.on('error', () => {
        resolve(false);
      });
    });
  }

  /**
   * Wait for DNS propagation
   */
  private async waitForDNS(dnsName: string, expectedIP: string, maxAttempts: number = 10): Promise<boolean> {
    const { lookup } = require('dns').promises;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const result = await lookup(dnsName);
        if (result.address === expectedIP) {
          this.logger.log(`🎯 DNS resolved correctly: ${dnsName} -> ${expectedIP}`);
          return true;
        }
        this.logger.log(`📡 DNS attempt ${attempt}: ${dnsName} -> ${result.address} (expected ${expectedIP})`);
      } catch (error) {
        this.logger.log(`📡 DNS attempt ${attempt}: ${dnsName} not resolved yet`);
      }

      await this.sleep(5000);
    }

    return false;
  }

  /**
   * Run the actual Ansible provisioning playbook using temporary static inventory
   */
  private async runProvisioningPlaybook(
    serverId: string,
    serverType: string,
    ipAddress: string,
    dnsName: string,
    serverName?: string
  ): Promise<AnsibleExecutionResult> {
    const playbookPath = path.join(this.ansiblePath, 'playbooks/bbb-provision.yml');

    // Create temporary static inventory file to bypass dynamic inventory targeting issues
    const tempInventoryPath = await this.createTempInventory(serverId, ipAddress);

    try {
      const options: AnsiblePlaybookOptions = {
        inventory: tempInventoryPath, // Use temporary static inventory
        playbook: playbookPath,
        variables: {
          server_id: serverId,
          server_type: this.mapServerType(serverType),
          ansible_host: ipAddress,
          ...(dnsName && { dns_name: dnsName }), // Only include dns_name if it exists
          server_name: serverName || dnsName || serverId, // Use server name, fallback to DNS name, then serverId
          base_domain: 'geerd.net',
          email: '<EMAIL>'
        },
        options: [
          '--limit', serverId, // Target by server ID (hostname in inventory)
          '--timeout', '900', // 15 minutes for provisioning
          '--ssh-extra-args', '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=30',
          '--become',
          '--become-method', 'sudo',
          '-vv' // Increase verbosity for debugging
        ]
      };

      return await this.executePlaybook(options);
    } finally {
      // Always clean up the temporary inventory file
      await this.cleanupTempInventory(tempInventoryPath);
    }
  }

  /**
   * Verify BBB services are running properly
   */
  private async verifyBBBServices(ipAddress: string): Promise<boolean> {
    try {
      const result = await this.testSSHCommand(ipAddress, 'bbb-conf --check');
      return result.includes('BigBlueButton');
    } catch (error) {
      this.logger.warn(`BBB service verification failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Execute SSH command on remote server
   */
  private async testSSHCommand(ipAddress: string, command: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const sshProcess = spawn('ssh', [
        '-o', 'ConnectTimeout=10',
        '-o', 'StrictHostKeyChecking=no',
        '-o', 'UserKnownHostsFile=/dev/null',
        `root@${ipAddress}`,
        command
      ]);

      let output = '';
      let errorOutput = '';

      sshProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      sshProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      sshProcess.on('close', (code) => {
        if (code === 0) {
          resolve(output);
        } else {
          reject(new Error(`SSH command failed: ${errorOutput}`));
        }
      });

      sshProcess.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Schedule server decommissioning 5 minutes before lecture ends
   * @param serverId The server ID
   * @param ipAddress The server IP address
   * @param lectureEndTime When the lecture is scheduled to end
   * @returns Promise<AnsibleExecutionResult>
   */
  async scheduleDecommissioning(
    serverId: string,
    ipAddress: string,
    lectureEndTime: Date
  ): Promise<AnsibleExecutionResult> {
    const now = new Date();
    const decommissionTime = new Date(lectureEndTime.getTime() - 5 * 60 * 1000); // 5 minutes before
    const timeUntilDecommission = decommissionTime.getTime() - now.getTime();

    this.logger.log(`📅 Scheduling decommissioning for server ${serverId} at ${decommissionTime.toISOString()}`);
    this.logger.log(`⏰ Time until decommissioning: ${Math.round(timeUntilDecommission / 1000 / 60)} minutes`);

    if (timeUntilDecommission <= 0) {
      // If lecture is ending soon, decommission immediately
      this.logger.log(`🚨 Lecture ending soon, starting immediate graceful decommissioning`);
      return this.decommissionServer(serverId, ipAddress, false);
    }

    // Wait until 5 minutes before lecture ends
    await this.sleep(timeUntilDecommission);

    this.logger.log(`⚠️ Starting graceful decommissioning for server ${serverId} (5min warning)`);
    return this.decommissionServer(serverId, ipAddress, false);
  }

  /**
   * Decommission a BigBlueButton server using Ansible with graceful shutdown
   * @param serverId The server ID
   * @param ipAddress The server IP address
   * @param forceDecommission Whether to force decommission even with active meetings
   * @param serverName The DNS name of the server (e.g., bbb-medium-1750689000344)
   * @returns Promise<AnsibleExecutionResult>
   */
  async decommissionServer(
    serverId: string,
    ipAddress: string,
    forceDecommission: boolean = false,
    serverName?: string
  ): Promise<AnsibleExecutionResult> {
    this.logger.log(`🔄 Starting decommissioning for server ${serverId} at ${ipAddress}`);

    try {
      // Run decommissioning playbook (handles everything: meetings, recordings, scalelite, services)
      this.logger.log(`⚙️ Running Ansible decommissioning playbook...`);
      const result = await this.runDecommissioningPlaybook(serverId, ipAddress, forceDecommission, serverName);

      if (result.success) {
        this.logger.log(`✅ Successfully decommissioned server ${serverId}`);
      } else {
        this.logger.error(`❌ Failed to decommission server ${serverId}: ${result.error}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`❌ Error during server decommissioning: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        output: [],
        duration: 0
      };
    }
  }

  /**
   * Smart server spindown - waits for appropriate time then calls decommission
   * @param serverId The server ID
   * @param ipAddress The server IP address
   * @param lectureStartTime When the lecture was scheduled to start
   * @param lectureEndTime When the lecture was scheduled to end
   * @param maxWaitTime Maximum time to wait for meetings to end (default: 30 minutes)
   * @returns Promise<AnsibleExecutionResult>
   */
  async smartServerSpindown(
    serverId: string,
    ipAddress: string,
    lectureStartTime: Date,
    lectureEndTime: Date,
    maxWaitTime: number = 30 * 60 * 1000 // 30 minutes in milliseconds
  ): Promise<AnsibleExecutionResult> {
    const now = new Date();
    const endTimeWithBuffer = new Date(lectureEndTime.getTime() + 5 * 60 * 1000); // Add 5 minutes buffer
    const maxWaitUntil = new Date(lectureEndTime.getTime() + maxWaitTime);

    this.logger.log(`🕐 Smart spindown initiated for server ${serverId}`);
    this.logger.log(`📅 Lecture ended: ${lectureEndTime.toISOString()}, Buffer until: ${endTimeWithBuffer.toISOString()}`);

    try {
      // Wait until end time + 5 minutes if needed
      if (now < endTimeWithBuffer) {
        const waitTime = endTimeWithBuffer.getTime() - now.getTime();
        this.logger.log(`⏳ Waiting ${Math.round(waitTime / 1000 / 60)} minutes until buffer period ends`);
        await this.sleep(waitTime);
      }

      // Monitor for active meetings with intelligent waiting
      let checkCount = 0;
      const maxChecks = Math.floor(maxWaitTime / (2 * 60 * 1000)); // Check every 2 minutes
      let lastLogTime = 0;

      while (checkCount < maxChecks) {
        const currentTime = new Date();

        // Stop waiting if we've exceeded maximum wait time
        if (currentTime > maxWaitUntil) {
          this.logger.warn(`⚠️ Maximum wait time exceeded, proceeding with decommission`);
          break;
        }

        // Check for active meetings using simple SSH command
        const hasActiveMeetings = await this.checkActiveMeetingsSimple(ipAddress);

        if (!hasActiveMeetings) {
          this.logger.log(`✅ No active meetings detected, proceeding with decommission`);
          break;
        }

        checkCount++;
        const remainingTime = Math.round((maxWaitUntil.getTime() - currentTime.getTime()) / 1000 / 60);

        // Log only every 6 minutes to reduce noise
        const nowTime = Date.now();
        if (nowTime - lastLogTime > 6 * 60 * 1000) {
          this.logger.log(`👥 Active meetings detected - waiting up to ${remainingTime} more minutes`);
          lastLogTime = nowTime;
        }

        // Wait 2 minutes before next check
        await this.sleep(2 * 60 * 1000);
      }

      // Run the decommission process (playbook handles everything)
      this.logger.log(`🔄 Starting decommission process for ${serverId}`);
      return await this.decommissionServer(serverId, ipAddress, false);

    } catch (error) {
      this.logger.error(`❌ Error during smart spindown: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        output: [],
        duration: 0
      };
    }
  }

  /**
   * Simple active meeting check for smart spindown
   */
  private async checkActiveMeetingsSimple(ipAddress: string): Promise<boolean> {
    try {
      const result = await this.testSSHCommand(
        ipAddress,
        'bbb-conf --check | grep -E "Current meetings:|Users currently in:" | head -2'
      );

      const lines = result.split('\n').filter(line => line.trim());

      for (const line of lines) {
        const trimmed = line.trim();

        if (trimmed.includes('Current meetings:')) {
          const meetingMatch = trimmed.match(/Current meetings:\s*(\d+)/);
          if (meetingMatch && parseInt(meetingMatch[1]) > 0) {
            return true;
          }
        }

        if (trimmed.includes('Users currently in:')) {
          const userMatch = trimmed.match(/Users currently in:\s*(\d+)/);
          if (userMatch && parseInt(userMatch[1]) > 0) {
            return true;
          }
        }
      }

      return false;
    } catch (error) {
      this.logger.warn(`⚠️ Could not check meetings, assuming safe to proceed: ${error.message}`);
      return false;
    }
  }

  /**
   * Schedule smart server spindown to run automatically
   * @param serverId The server ID
   * @param ipAddress The server IP address
   * @param lectureStartTime When the lecture was scheduled to start
   * @param lectureEndTime When the lecture was scheduled to end
   * @returns Promise<void>
   */
  async scheduleSmartSpindown(
    serverId: string,
    ipAddress: string,
    lectureStartTime: Date,
    lectureEndTime: Date
  ): Promise<void> {
    const now = new Date();
    const spindownStartTime = new Date(lectureEndTime.getTime() + 5 * 60 * 1000); // Start checking 5 minutes after lecture end
    const timeUntilSpindown = spindownStartTime.getTime() - now.getTime();

    this.logger.log(`📅 Scheduling smart spindown for server ${serverId}`);
    this.logger.log(`🕐 Will start monitoring at: ${spindownStartTime.toISOString()}`);
    this.logger.log(`⏰ Time until monitoring starts: ${Math.round(timeUntilSpindown / 1000 / 60)} minutes`);

    if (timeUntilSpindown > 0) {
      // Wait until it's time to start monitoring
      setTimeout(async () => {
        this.logger.log(`🚀 Starting smart spindown monitoring for server ${serverId}`);
        try {
          await this.smartServerSpindown(serverId, ipAddress, lectureStartTime, lectureEndTime);
        } catch (error) {
          this.logger.error(`❌ Scheduled smart spindown failed for server ${serverId}:`, error);
        }
      }, timeUntilSpindown);
    } else {
      // Lecture has already ended, start monitoring immediately
      this.logger.log(`🚨 Lecture already ended, starting immediate smart spindown monitoring`);
      setImmediate(async () => {
        try {
          await this.smartServerSpindown(serverId, ipAddress, lectureStartTime, lectureEndTime);
        } catch (error) {
          this.logger.error(`❌ Immediate smart spindown failed for server ${serverId}:`, error);
        }
      });
    }
  }

  /**
   * Check for active meetings reliably using BBB's built-in status check
   */
  private async checkActiveMeetings(ipAddress: string): Promise<boolean> {
    try {
      const result = await this.testSSHCommand(
        ipAddress,
        'bbb-conf --check | grep -E "Current meetings:|Users currently in:" | head -2'
      );

      const lines = result.split('\n').filter(line => line.trim());

      for (const line of lines) {
        const trimmed = line.trim();

        // Check for active meetings
        if (trimmed.includes('Current meetings:')) {
          const meetingMatch = trimmed.match(/Current meetings:\s*(\d+)/);
          if (meetingMatch && parseInt(meetingMatch[1]) > 0) {
            this.logger.log(`� Active meetings: ${meetingMatch[1]}`);
            return true;
          }
        }

        // Check for active users
        if (trimmed.includes('Users currently in:')) {
          const userMatch = trimmed.match(/Users currently in:\s*(\d+)/);
          if (userMatch && parseInt(userMatch[1]) > 0) {
            this.logger.log(`👤 Active users: ${userMatch[1]}`);
            return true;
          }
        }
      }

      this.logger.debug(`✅ No active meetings or users detected`);
      return false;
    } catch (error) {
      this.logger.warn(`⚠️ Could not check active meetings, assuming safe to proceed: ${error.message}`);
      return false; // Fail safe - assume no meetings if we can't check
    }
  }

  /**
   * Transfer recordings safely to remote server
   */
  private async transferRecordings(ipAddress: string): Promise<void> {
    try {
      // Check if there are recordings to transfer using more reliable method
      const recordingCheck = await this.testSSHCommand(
        ipAddress,
        'find /var/bigbluebutton/recording/raw -name "*.tar" 2>/dev/null | wc -l'
      );

      const recordingCount = parseInt(recordingCheck.trim());

      if (recordingCount > 0) {
        this.logger.log(`📼 Found ${recordingCount} recordings to transfer`);

        // Stop the worker to prevent conflicts
        await this.testSSHCommand(ipAddress, 'systemctl stop bbb-rap-resque-worker || true');

        // Run a test to verify the upload script exists (--all is not a valid option)
        await this.testSSHCommand(
          ipAddress,
          'test -f /usr/local/bigbluebutton/core/scripts/post_archive/upload_raw_recording.rb && echo "Upload script exists"'
        );

        this.logger.log(`📼 Recording transfer completed`);
      } else {
        this.logger.log(`📼 No recordings found to transfer`);
      }
    } catch (error) {
      this.logger.warn(`Failed to transfer recordings: ${error.message}`);
      // Don't throw - recording transfer failure shouldn't block decommission
    }
  }

  /**
   * Remove server from Scalelite load balancer via decommission playbook
   */
  private async removeFromLoadBalancer(serverId: string, ipAddress: string): Promise<void> {
    try {
      // Use the main decommission playbook which handles Scalelite removal
      const decommissionPlaybook = path.join(this.ansiblePath, 'playbooks/bbb-decommission.yml');

      // Create temporary inventory with serverId as hostname
      const tempInventoryPath = await this.createTempInventory(serverId, ipAddress);

      try {
        const options: AnsiblePlaybookOptions = {
          inventory: tempInventoryPath, // Use temporary inventory
          playbook: decommissionPlaybook,
          variables: {
            server_id: serverId,
            ansible_host: ipAddress,
            // Only run Scalelite removal tasks
            scalelite_only: true,
            force_decommission: true // Skip meeting checks for load balancer removal
          },
          options: [
            '--limit', serverId, // Target by server name/hostname
            '--timeout', '300',
            '--tags', 'scalelite', // Only run scalelite-related tasks
            '--ssh-extra-args', '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
          ]
        };

        const result = await this.executePlaybook(options);
        if (result.success) {
          this.logger.log(`✅ Server removed from load balancer`);
        } else {
          this.logger.warn(`⚠️ Failed to remove from load balancer: ${result.error}`);
        }
      } finally {
        // Always clean up the temporary inventory file
        await this.cleanupTempInventory(tempInventoryPath);
      }
    } catch (error) {
      this.logger.warn(`Failed to remove from load balancer: ${error.message}`);
    }
  }

  /**
   * Run the actual decommissioning playbook using temporary static inventory
   */
  private async runDecommissioningPlaybook(
    serverId: string,
    ipAddress: string,
    forceDecommission: boolean,
    serverName?: string
  ): Promise<AnsibleExecutionResult> {
    const playbookPath = path.join(this.ansiblePath, 'playbooks/bbb-decommission.yml');

    // Create temporary static inventory file to bypass dynamic inventory targeting issues
    const tempInventoryPath = await this.createTempInventory(serverId, ipAddress);

    try {
      const options: AnsiblePlaybookOptions = {
        inventory: tempInventoryPath, // Use temporary static inventory
        playbook: playbookPath,
        variables: {
          server_id: serverId,
          ansible_host: ipAddress,
          force_decommission: forceDecommission,
          base_domain: 'geerd.net',
          dns_name: serverName, // Pass the DNS name to the playbook
          scalelite_action: 'unregister', // Explicitly set for decommissioning
          server_name: serverName, // Also pass as server_name for compatibility
          // Storage configuration for recording transfer
          storage: {
            type: 'spaces',
            bucket_name: this.configService.get<string>('DO_SPACES_BUCKET', 'bbb-terraform'),
            region: this.configService.get<string>('DO_SPACES_REGION', 'fra1'),
            endpoint: this.configService.get<string>('DO_SPACES_ENDPOINT', 'https://fra1.digitaloceanspaces.com'),
            access_key: this.configService.get<string>('DO_SPACES_ACCESS_KEY'),
            secret_key: this.configService.get<string>('DO_SPACES_SECRET_KEY')
          }
        },
        options: [
          '--limit', serverId, // Target by server ID (hostname in inventory)
          '--timeout', '900', // 15 minutes timeout for decommissioning
          '--ssh-extra-args', '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null',
          '--become',
          '--become-method', 'sudo',
          '-v' // Add verbosity for debugging
        ]
      };

      return await this.executePlaybook(options);
    } finally {
      // Always clean up the temporary inventory file
      await this.cleanupTempInventory(tempInventoryPath);
    }
  }

  /**
   * Execute an Ansible playbook
   * @param options The playbook execution options
   * @returns Promise<AnsibleExecutionResult>
   */
  private async executePlaybook(options: AnsiblePlaybookOptions): Promise<AnsibleExecutionResult> {
    const startTime = Date.now();
    let output: string[] = [];

    try {
      this.logger.log(`Executing Ansible playbook: ${options.playbook}`);
      this.logger.log(`Inventory: ${options.inventory}`);
      this.logger.log(`Variables: ${JSON.stringify(options.variables, null, 2)}`);

      // Build ansible-playbook command arguments
      const args = [
        options.playbook,
        '-i', options.inventory
      ];

      // Add variables as extra-vars
      if (options.variables) {
        const extraVars = Object.entries(options.variables)
          .map(([key, value]) => {
            if (typeof value === 'object') {
              return `${key}='${JSON.stringify(value)}'`;
            } else {
              return `${key}='${value}'`;
            }
          })
          .join(' ');
        args.push('--extra-vars', extraVars);
      }

      // Add options
      if (options.options) {
        args.push(...options.options);
      }

      // Set essential environment variables for Ansible (minimal set for security)
      const env = {
        ...process.env,
        // Ansible configuration
        ANSIBLE_HOST_KEY_CHECKING: 'False',
        ANSIBLE_SSH_RETRIES: '3',
        ANSIBLE_TIMEOUT: '30',
        ANSIBLE_GATHERING: 'smart',
        ANSIBLE_STDOUT_CALLBACK: 'yaml',
        ANSIBLE_ROLES_PATH: path.join(this.ansiblePath, 'roles'),
        // Only pass essential API tokens (managed securely via config service)
        DO_API_TOKEN: this.configService.get<string>('DIGITALOCEAN_API_TOKEN')
      };

      this.logger.log(`Running command: ansible-playbook ${args.join(' ')}`);

      return new Promise((resolve) => {
        const ansibleProcess = spawn('ansible-playbook', args, {
          env,
          cwd: this.ansiblePath,
          stdio: ['pipe', 'pipe', 'pipe']
        });

        ansibleProcess.stdout.on('data', (data: Buffer) => {
          const line = data.toString();
          output.push(line);
          this.logger.log(`Ansible stdout: ${line.trim()}`);
        });

        ansibleProcess.stderr.on('data', (data: Buffer) => {
          const line = data.toString();
          output.push(line);
          this.logger.warn(`Ansible stderr: ${line.trim()}`);
        });

        ansibleProcess.on('error', (error) => {
          const duration = Date.now() - startTime;
          this.logger.error(`Ansible process error: ${error.message}`, error.stack);

          resolve({
            success: false,
            error: error.message,
            output,
            duration
          });
        });

        ansibleProcess.on('close', (code) => {
          const duration = Date.now() - startTime;
          const success = code === 0;

          if (success) {
            this.logger.log(`Ansible playbook completed successfully in ${duration}ms`);
          } else {
            this.logger.error(`Ansible playbook failed with exit code ${code} after ${duration}ms`);
          }

          resolve({
            success,
            error: success ? undefined : `Ansible playbook failed with exit code ${code}`,
            output,
            duration,
            exitCode: code
          });
        });
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Ansible execution error: ${error.message}`, error.stack);

      return {
        success: false,
        error: error.message,
        output,
        duration
      };
    }
  }

  /**
   * Map server size to Ansible server type
   * @param serverSize The server size from the scheduler
   * @returns The corresponding Ansible server type
   */
  private mapServerType(serverSize: string): string {
    const mapping: Record<string, string> = {
      'medium': 'medium',
      'large': 'large',
      'extra-large': 'extra_large'
    };

    return mapping[serverSize] || 'medium';
  }  /**
   * Validate Ansible installation and connectivity
   * @returns Promise<boolean>
   */
  async validateAnsibleSetup(): Promise<boolean> {
    try {
      this.logger.log('Validating Ansible setup...');

      // Check if Ansible is installed by running a simple command
      return new Promise((resolve) => {
        const ansibleProcess = spawn('ansible', ['--version'], {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        let output = '';
        let errorOutput = '';

        ansibleProcess.stdout.on('data', (data: Buffer) => {
          output += data.toString();
        });

        ansibleProcess.stderr.on('data', (data: Buffer) => {
          errorOutput += data.toString();
        });

        ansibleProcess.on('error', (error) => {
          this.logger.error(`Ansible validation error: ${error.message}`);
          resolve(false);
        });

        ansibleProcess.on('close', (code) => {
          const isValid = code === 0;
          if (isValid) {
            this.logger.log('Ansible setup validation successful');
            this.logger.debug(`Ansible version output: ${output.trim()}`);
          } else {
            this.logger.error('Ansible setup validation failed');
            this.logger.error(`Error output: ${errorOutput.trim()}`);
          }
          resolve(isValid);
        });
      });
    } catch (error) {
      this.logger.error(`Ansible setup validation error: ${error.message}`);
      return false;
    }
  }

  /**
   * Get cron job status and next execution time
   */
  getCronJobStatus(): any {
    return {
      name: 'bbb-scheduler',
      schedule: '*/5 * * * *', // Every 5 minutes
      timezone: 'Africa/Casablanca',
      description: 'BBB server provisioning and decommissioning scheduler',
      nextRun: this.getNextCronRun(),
      enabled: true
    };
  }

  /**
   * Calculate next cron job execution time
   */
  private getNextCronRun(): string {
    const now = new Date();
    const nextRun = new Date(now);

    // Add 5 minutes to current time
    nextRun.setMinutes(nextRun.getMinutes() + 5);

    return nextRun.toISOString();
  }

  /**
   * Drain server by disabling new meetings in load balancer
   */
  private async drainServer(ipAddress: string): Promise<void> {
    try {
      this.logger.log(`🚰 Draining server ${ipAddress} - disabling new meeting assignments`);

      // This would typically disable the server in Scalelite without removing it
      // For now, we'll implement a basic drain mechanism
      await this.testSSHCommand(ipAddress, 'echo "Server entering drain mode"');

      // In a real implementation, you would call Scalelite to disable the server
      // but keep it registered so existing meetings can continue
      this.logger.log(`✅ Server ${ipAddress} is now in drain mode`);
    } catch (error) {
      this.logger.warn(`Failed to drain server: ${error.message}`);
    }
  }

  /**
   * Public wrapper for checking active meetings on a server
   * @param ipAddress The server IP address
   * @returns Promise<boolean>
   */
  async checkServerMeetings(ipAddress: string): Promise<boolean> {
    return this.checkActiveMeetings(ipAddress);
  }

  /**
   * Create temporary inventory file for server targeting
   * This ensures consistent hostname-based targeting instead of IP-based targeting
   */
  private async createTempInventory(serverName: string, ipAddress: string): Promise<string> {
    const fs = require('fs');
    const os = require('os');

    // Create a temporary inventory file in the system temp directory
    const tempDir = os.tmpdir();
    const timestamp = new Date().getTime();
    const tempInventoryPath = `${tempDir}/ansible-inventory-${serverName}-${timestamp}.ini`;

    // Create inventory content with server name as hostname
    const inventoryContent = `[bbb_servers]
${serverName} ansible_host=${ipAddress} ansible_user=root
`;

    // Write the temporary inventory file
    fs.writeFileSync(tempInventoryPath, inventoryContent);
    this.logger.log(`📋 Created temporary inventory: ${tempInventoryPath} for server ${serverName}`);

    return tempInventoryPath;
  }

  /**
   * Clean up temporary inventory file
   */
  private async cleanupTempInventory(tempInventoryPath: string): Promise<void> {
    try {
      const fs = require('fs');
      if (fs.existsSync(tempInventoryPath)) {
        fs.unlinkSync(tempInventoryPath);
        this.logger.log(`🗑️ Cleaned up temporary inventory: ${tempInventoryPath}`);
      }
    } catch (error) {
      this.logger.warn(`⚠️ Failed to cleanup temporary inventory ${tempInventoryPath}: ${error.message}`);
    }
  }
}
