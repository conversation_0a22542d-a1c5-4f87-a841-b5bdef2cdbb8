import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { BbbSchedulerService } from './bbb-scheduler.service';
import { BbbSchedulerController } from './bbb-scheduler.controller';
import { HttpModule } from '@nestjs/axios';
import { DnsModule } from '../dns';
import { CacheModule } from '../cache';
import { AnsibleModule } from '../ansible';

@Module({
  imports: [
    HttpModule,
    ConfigModule.forRoot(),
    DnsModule,
    CacheModule,
    AnsibleModule,
  ],
  controllers: [BbbSchedulerController],
  providers: [BbbSchedulerService],
  exports: [BbbSchedulerService],
})
export class BbbSchedulerModule {}
