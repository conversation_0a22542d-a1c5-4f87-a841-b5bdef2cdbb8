import { Controller, Post, Get, Param, Body } from '@nestjs/common';
import { BbbSchedulerService } from './bbb-scheduler.service';
import { AnsibleService } from '../ansible/ansible.service';

@Controller('bbb-scheduler')
export class BbbSchedulerController {
  constructor(
    private readonly bbbSchedulerService: BbbSchedulerService,
    private readonly ansibleService: AnsibleService
  ) {}

  @Post('trigger')
  async triggerScheduler() {
    return this.bbbSchedulerService.scheduleDailyServers();
  }

  @Get('status')
  getStatus() {
    return this.bbbSchedulerService.getStatus();
  }

  // Note: Processing is handled automatically by standalone processing server

  /**
   * Get cron job status
   */
  @Get('cron-status')
  async getCronStatus() {
    return this.ansibleService.getCronJobStatus();
  }

  /**
   * Smart server spindown with lecture time monitoring
   */
  @Post('smart-spindown/:serverId/:ipAddress')
  async smartSpindown(
    @Param('serverId') serverId: string,
    @Param('ipAddress') ipAddress: string,
    @Body() body: {
      lectureStartTime: string;
      lectureEndTime: string;
      maxWaitTime?: number
    }
  ) {
    const lectureStart = new Date(body.lectureStartTime);
    const lectureEnd = new Date(body.lectureEndTime);
    const maxWait = body.maxWaitTime || 30 * 60 * 1000; // 30 minutes default

    const result = await this.ansibleService.smartServerSpindown(
      serverId,
      ipAddress,
      lectureStart,
      lectureEnd,
      maxWait
    );

    return {
      success: result.success,
      message: result.success
        ? `Smart spindown completed for server ${serverId}`
        : result.error,
      timestamp: new Date().toISOString(),
      lectureInfo: {
        startTime: lectureStart.toISOString(),
        endTime: lectureEnd.toISOString(),
        endTimeWithBuffer: new Date(lectureEnd.getTime() + 5 * 60 * 1000).toISOString()
      }
    };
  }

  /**
   * Schedule smart server spindown for automatic execution
   */
  @Post('schedule-spindown/:serverId/:ipAddress')
  async scheduleSpindown(
    @Param('serverId') serverId: string,
    @Param('ipAddress') ipAddress: string,
    @Body() body: {
      lectureStartTime: string;
      lectureEndTime: string
    }
  ) {
    const lectureStart = new Date(body.lectureStartTime);
    const lectureEnd = new Date(body.lectureEndTime);

    await this.ansibleService.scheduleSmartSpindown(
      serverId,
      ipAddress,
      lectureStart,
      lectureEnd
    );

    return {
      success: true,
      message: `Smart spindown scheduled for server ${serverId}`,
      timestamp: new Date().toISOString(),
      lectureInfo: {
        startTime: lectureStart.toISOString(),
        endTime: lectureEnd.toISOString(),
        spindownMonitoringStart: new Date(lectureEnd.getTime() + 5 * 60 * 1000).toISOString()
      }
    };
  }

  /**
   * Check active meetings on a specific server
   */
  @Get('check-meetings/:ipAddress')
  async checkMeetings(@Param('ipAddress') ipAddress: string) {
    // We need to access the private method, so let's create a public wrapper
    const hasActiveMeetings = await this.ansibleService.checkServerMeetings(ipAddress);

    return {
      hasActiveMeetings,
      timestamp: new Date().toISOString(),
      server: ipAddress
    };
  }
}
