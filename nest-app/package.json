{"name": "bbb-manager", "version": "0.0.1", "description": "A NestJS service for managing BigBlueButton servers", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "mock-api": "ts-node src/mock/mock-api.ts", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/axios": "^0.0.8", "@nestjs/common": "^8.0.0", "@nestjs/config": "^2.0.0", "@nestjs/core": "^8.0.0", "@nestjs/platform-express": "^8.0.0", "@nestjs/schedule": "^2.2.3", "@types/node-cron": "^3.0.11", "axios": "^0.26.1", "ioredis": "^5.6.1", "node-ansible": "^0.5.5", "node-cron": "^4.1.0", "redis": "^4.7.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "ssh2": "^1.16.0", "webpack": "^5.99.5"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@types/express": "^4.17.13", "@types/jest": "27.5.0", "@types/node": "^16.0.0", "@types/ssh2": "^1.11.7", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "28.0.3", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.1", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}