# BBB Manager Production Environment Configuration
# Copy this file to .env and fill in your actual values

# ================================
# APPLICATION SETTINGS
# ================================
NODE_ENV=production
PORT=3000

# ================================
# DIGITALOCEAN CONFIGURATION
# ================================
# Your DigitalOcean API token for creating/managing droplets
DIGITALOCEAN_API_TOKEN=your_digitalocean_api_token_here

# Base domain for your servers (e.g., geerd.net)
DIGITALOCEAN_BASE_DOMAIN=geerd.net

# ================================
# HASURA GRAPHQL DATABASE
# ================================
# Hasura GraphQL endpoint for lecture data
HASURA_GRAPHQL_ENDPOINT=https://your-hasura-endpoint.hasura.app/v1/graphql

# Hasura admin secret
HASURA_GRAPHQL_ADMIN_SECRET=your_hasura_admin_secret

# ================================
# REDIS CACHE CONFIGURATION
# ================================
REDIS_HOST=redis
REDIS_PORT=6379

# ================================
# SCALELITE LOAD BALANCER
# ================================
# IP address of your Scalelite load balancer
SCALELITE_SERVER_IP=**************

# SSH password for Scalelite server access
SCALELITE_SSH_PASSWORD=your_scalelite_ssh_password

# ================================
# PROCESSING SERVER
# ================================
# IP address of your video processing server
PROCESSING_SERVER_IP=************

# SSH password for processing server access
PROCESSING_SERVER_PASSWORD=your_processing_server_password

# ================================
# DIGITALOCEAN SPACES (Object Storage)
# ================================
# Spaces bucket name for storing recordings
SPACES_BUCKET_NAME=bbb-terraform

# Spaces region (e.g., fra1, nyc3, sgp1)
SPACES_REGION=fra1

# Spaces access key (from DigitalOcean API Keys & Tokens)
SPACES_ACCESS_KEY=your_spaces_access_key

# Spaces secret key (from DigitalOcean API Keys & Tokens)
SPACES_SECRET_KEY=your_spaces_secret_key

# ================================
# ANSIBLE CONFIGURATION (Optional Overrides)
# ================================
# These are set automatically but can be overridden if needed

# Path to Ansible playbooks inside container
# ANSIBLE_PLAYBOOK_PATH=/app/bbb-ansible/playbooks

# Path to Ansible inventory inside container
# ANSIBLE_INVENTORY_PATH=/app/bbb-ansible/inventory

# Path to Ansible roles inside container
# ANSIBLE_ROLES_PATH=/app/bbb-ansible/roles

# Disable SSH host key checking for dynamic servers
# ANSIBLE_HOST_KEY_CHECKING=False

# Number of SSH connection retries
# ANSIBLE_SSH_RETRIES=3
