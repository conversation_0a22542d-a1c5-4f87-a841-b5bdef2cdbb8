# ================================================================================================
# BBB Manager Production Docker Compose Configuration
# ================================================================================================
# This configuration sets up the complete BBB Manager system for production deployment
# including the main application, Redis cache, and all necessary networking

version: '3.8'

services:
  # ================================================================================================
  # BBB Manager Application - Main orchestration service
  # ================================================================================================
  bbb-manager:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: bbb-manager:latest
    container_name: bbb-manager-app
    restart: unless-stopped
    
    # Environment configuration
    env_file:
      - .env.production
    
    # Port mapping
    ports:
      - "3000:3000"
    
    # Volume mounts for persistent data and SSH keys
    volumes:
      # Persistent logs
      - bbb-logs:/app/logs
      # Temporary files
      - bbb-temp:/app/tmp
      # SSH keys for server access (if using key-based auth)
      - ./ssh-keys:/home/<USER>/.ssh:ro
      # Ansible inventory cache
      - bbb-ansible-cache:/home/<USER>/.ansible
    
    # Dependencies
    depends_on:
      redis:
        condition: service_healthy
    
    # Networking
    networks:
      - bbb-network
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/bbb-scheduler/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ================================================================================================
  # Redis Cache - For lecture data and server status caching
  # ================================================================================================
  redis:
    image: redis:7-alpine
    container_name: bbb-manager-redis
    restart: unless-stopped
    
    # Redis configuration
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    
    # Volume for data persistence
    volumes:
      - redis-data:/data
    
    # Networking
    networks:
      - bbb-network
    
    # Health check
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.2'
        reservations:
          memory: 128M
          cpus: '0.1'
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

  # ================================================================================================
  # Redis Commander - Web UI for Redis management (Optional - Remove in production)
  # ================================================================================================
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: bbb-manager-redis-ui
    restart: unless-stopped
    
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=secure_password_here
    
    ports:
      - "8081:8081"
    
    depends_on:
      redis:
        condition: service_healthy
    
    networks:
      - bbb-network
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'
    
    # Comment out this entire service in production for security
    profiles:
      - debug

# ================================================================================================
# Named Volumes - Persistent data storage
# ================================================================================================
volumes:
  # Application logs
  bbb-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs
  
  # Temporary files
  bbb-temp:
    driver: local
  
  # Redis data persistence
  redis-data:
    driver: local
  
  # Ansible cache
  bbb-ansible-cache:
    driver: local

# ================================================================================================
# Networks - Isolated networking for services
# ================================================================================================
networks:
  bbb-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
